.data-filter-container {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid #e9ecef;
}

.input-group-text {
  background-color: #fff;
  border-color: #ced4da;
}

.btn-outline-primary:hover {
  color: #fff;
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.btn-outline-secondary:hover {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.card.border-primary {
  border-color: #0d6efd !important;
}

.card-header.bg-light {
  background-color: #f8f9fa !important;
  border-bottom: 1px solid #dee2e6;
}

.form-select:focus,
.form-control:focus {
  border-color: #86b7fe;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

@media (max-width: 768px) {
  .data-filter-container {
    padding: 0.75rem;
  }
  
  .d-flex.gap-2 {
    flex-direction: column;
    gap: 0.5rem !important;
  }
  
  .btn {
    width: 100%;
  }
}
