<app-layout>

<!-- Bouton pour ouvrir la modale -->

<div class="header-container">
  <div class="header-text">
    <h2>Gestion des Conseils</h2>
    <p>Module 1 - Gestion des Planification et Conseil</p>
  </div>
<button class="add-user-btn"  [routerLink]="'/ajoutConseil'">
  <span class="icon">👤</span> Nouveau Conseil
</button>
</div>


<style>

   .body {
      font-family: 'Arial', sans-serif;
      background: #f5f5f5;
      padding: 30px;
    }

    .card {
      background: #fff;
      width: 1100px;
      margin: auto;
      padding: 20px;
      border-radius: 12px;
      border: 1px solid #e5e7eb;
      box-shadow: 0 2px 5px rgba(0,0,0,0.05);
      margin-bottom: 25px; /* espace entre les cartes */
    }

    .header {
      display: flex;
      align-items: center;
      font-weight: bold;
      font-size: 16px;
      margin-bottom: 15px;
    }

    .header span {
      margin-left: 10px;
    }

    .status {
      margin-left: auto;
      background-color: #d1fae5;
      color: #065f46;
      padding: 4px 12px;
      border-radius: 999px;
      font-size: 13px;
      font-weight: 600;
    }

    .info-line {
      display: flex;
      justify-content: space-between;
      margin: 10px 0;
    }

    .info-block {
      flex: 1;
      text-align: center;
      font-size: 14px;
      color: #374151;
    }

    .info-block:first-child {
      text-align: left;
    }

.info-block:last-child {
  text-align: left; /* align text to left instead of right */
  margin-left: -30px; /* move it slightly to the left */
}
/* Move "Salle A101" slightly left */
.info-line:nth-of-type(1) .info-block:nth-child(2) {
  margin-left: -20px;
}

/* Move "12 participants" slightly left */
.info-line:nth-of-type(1) .info-block:nth-child(3) {
  margin-left: -300px;
}

/* Move "Rapporteur" slightly left */
.info-line:nth-of-type(2) .info-block:nth-child(2) {
  margin-left: -400px;
}


    .buttons {
      display: flex;
      gap: 10px;
      margin-top: 20px;
    }

    .modifier-btn,
    .confirm-btn {
      padding: 8px 16px;
      border-radius: 6px;
      border: 1px solid #d1d5db;
      background-color: white;
      cursor: pointer;
      font-size: 14px;
    }

    .confirm-btn {
      background-color: #0f172a;
      color: white;
      border: none;
    }
    .big-card {
  background-color: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.06);
  margin-top: 40px;
  width: 100%;
  max-width: 1300px;
  margin-left: auto;
  margin-right: auto;
}

  /* Move "12 participants" slightly left */
.info-line:nth-of-type(1) .info-block:nth-child(3) {
  margin-left: 30px;
}

/* Styles pour le modal d'affichage des participants */
.participants-display-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.participants-display-modal {
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.participants-display-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.participants-display-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.close-modal-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-modal-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.participant-display-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 12px;
  background-color: #fafafa;
}

.participant-display-info {
  flex: 1;
}

.participant-display-name {
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.participant-display-status {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
}

.status-display-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-display-badge.accepted {
  background-color: #d1fae5;
  color: #065f46;
}

.status-display-badge.rejected {
  background-color: #fee2e2;
  color: #991b1b;
}

.status-display-badge.pending {
  background-color: #fef3c7;
  color: #92400e;
}

.participant-display-justification {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #f8fafc;
  border-left: 3px solid #e2e8f0;
  border-radius: 4px;
  font-size: 13px;
  color: #64748b;
  font-style: italic;
}

.no-justification {
  color: #9ca3af;
}

.no-participants-display {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
  font-style: italic;
}

  </style>
          <!--  Row 1 -->
          <div class="row">
<div class="container">
    <!-- Calendrier de la semaine -->
  <div class="calendar">
  <h2>
    <img src="assets/images/logos/calendar.JPG" alt="Calendrier"
      style="width: 40px; height: 40px; vertical-align: middle; margin-right: 8px;">
    Calendrier de la semaine
  </h2>

  <p class="week">
    Semaine du {{ joursSemaine[0]?.date | date: 'dd MMMM yyyy' }} -
    {{ joursSemaine[6]?.date | date: 'dd MMMM yyyy' }}
  </p>

  <div class="days">
    <div *ngFor="let jour of joursSemaine"
         (click)="onSelectDate(jour.date)"
         [class.active]="selectedDate?.toDateString() === jour.date.toDateString()">
      {{ jour.label }}<br><span>{{ jour.date | date: 'dd' }}</span>
    </div>
  </div>

  <div *ngIf="conseilsFiltres.length === 0">
    <p>Aucun conseil pour ce jour.</p>
  </div>

<div *ngFor="let conseil of conseilsFiltres"
     class="event"
     [ngClass]="{'confirmed': conseil?.salle, 'pending': !conseil?.salle}">
  <strong>Classe : {{ conseil.classes }}</strong>

  <!-- Date et salle sous la classe, en petit et côte à côte -->
<div style="font-size: 0.85rem; color: #555; display: flex; align-items: center; gap: 8px; margin-top: 4px;">
  <span *ngIf="conseil.heure">Heure {{ conseil.heure }}</span>

  <!-- Trait vertical uniquement si les deux sont présents -->
  <span *ngIf="conseil.heure && conseil.salle" style="border-left: 1px solid #555; height: 12px;"></span>

  <span *ngIf="conseil.salle">Salle {{ conseil.salle.nomSalle }}</span>
</div>


  <span class="status" [ngClass]="{'confirmed': conseil?.salle, 'pending': !conseil?.salle}">
    {{ conseil?.salle ? 'Confirmé' : 'En attente' }}
  </span>
</div>


</div>


    <!-- Actions Rapides -->
    <div class="actions">
      <h2>Actions Rapides</h2>
      <p class="subtitle">Outils de planification</p>
     <button (click)="openCalendarModal()">
  <img src="assets/images/logos/calendar.JPG"  style="width: 20px; height: 20px; vertical-align: middle; margin-right: 5px;">
  Voir calendrier complet
</button>
     <button [routerLink]="['/listSalle']">
  <img src="assets/images/logos/position.JPG"  style="width: 23px; height: 25px; vertical-align: middle; margin-right: 5px;">
Gérer les salles
</button>

  <button (click)="openParticipantsModal()">
  <img src="assets/images/logos/mennn.JPG"  style="width: 23px; height: 25px; vertical-align: middle; margin-right: 5px;">
 Assigner participants
</button>
  <button>
  <img src="assets/images/logos/filter.JPG"  style="width: 23px; height: 25px; vertical-align: middle; margin-right: 5px;">
Filtrer par période
</button>
      
    </div>
  </div>  
  
  <div class="big-card">
  <!-- tout ton contenu ici (calendrier, actions, h2, *ngFor, etc.) -->



             <h2>Conseils Planifiés</h2>
  <div class="subtext">Liste détaillée des conseils de classe</div>


 
  <!-- Deuxième carte -->
<div *ngFor="let conseil of conseils" class="card">
  <div class="header">
    <img src="assets/images/logos/calendar.JPG" alt="Calendrier"
         style="width: 28px; height: 28px; vertical-align: middle; margin-right: 5px;">
         
    <div style="display: flex; flex-direction: column;">
      <span>{{ conseil.classes }}</span>
      <span style="font-size: 12px; color: #666;">Date:  {{ conseil.date }}| Heure {{ conseil.heure }}</span>
    </div>

    <span 
      class="status" 
      [ngClass]="conseil.etat ? 'confirmed' : 'unconfirmed'">
      {{ conseil.etat ? ' Confirmé' : ' Non Confirmé' }}
    </span>
  </div>


  <div class="info-line">
    <div class="info-block">🕒 Durée: {{ conseil.duree }}</div>
    <div class="info-block">
  <img src="assets/images/logos/position.JPG" alt="Salle" style="width: 23px; height: 29px; vertical-align: middle; margin-right: 6px;">
  Salle {{ conseil.salle?.nomSalle }}
</div>


<div class="info-block">
  <img src="assets/images/logos/mennn.JPG" alt="Participants" style="width: 23px; height: 29px; vertical-align: middle; margin-right: 6px;">
  {{ getConseilNumberParticipant(conseil) }} Invitations
</div>

  </div>

  <!-- Ligne Président - Rapporteur -->
<!-- Ligne Président - Rapporteur -->
<div class="info-line">
  <div class="info-block"><strong>Président:</strong> Prof.{{ getNomUtilisateur(conseil.presidentId) }} </div>
  <div class="info-block"><strong>Rapporteur:</strong> Dr.{{ getNomUtilisateur(conseil.raporteurId) }}</div>
</div>

  <!-- Boutons -->
  <div class="buttons">
    <button class="modifier-btn" (click)="modifierConseil(conseil.id)">Modifier</button>
        <button class="modifier-btn" (click)="DeleteConseil(conseil.id)" >supprimer</button>
    <button class="modifier-btn" (click)="openParticipantsDisplayModal(conseil)">Participants</button>
<button
  class="confirm-btn"
  (click)="Etatchnage(conseil.id, !conseil.etat)"
  [ngClass]="conseil.etat ? 'btn-confirmed' : 'btn-unconfirmed'"
>
  {{ conseil.etat ? '✅ Confirmé' : '❌ Non Confirmé' }}
</button>

  </div>
</div>

          </div>
          <div class="py-6 px-6 text-center">
            <p class="mb-0 fs-4">Design and Developed by <a href="#"
                class="pe-1 text-primary text-decoration-underline">Wrappixel.com</a> Distributed by <a href="https://themewagon.com" target="_blank" >ThemeWagon</a></p>
          </div>
        </div>


  <div class="participants-modal" [ngClass]="{'show': showParticipantsModal}" (click)="closeParticipantsModal()">
    <div class="participants-modal-content" (click)="$event.stopPropagation()">
      <div class="participants-header">
        <h3>Assigner les Participants aux Conseils</h3>
        <button class="close-btn" (click)="closeParticipantsModal()">&times;</button>
      </div>

      <div class="participants-actions">
        <button class="action-btn save-btn" (click)="saveParticipantsAssignments()">
           Sauvegarder les Assignations
        </button>
        <button class="action-btn clear-btn" (click)="clearAllAssignments()">
           Effacer Tout
        </button>
      </div>

<div class="participants-content">
  <div class="users-section-full">
    <h4>👥 Liste des Utilisateurs</h4>
    <div class="users-list">
      <div *ngFor="let user of getEnseigants()" class="user-card">
        <div class="user-info">
          <div class="user-name">{{ user.username }}</div>
          <div class="user-details">
            <span class="user-email">{{ user.email }}</span>
            <span class="user-role">{{ user.role }}</span>
          </div>
        </div>

        <div class="assignment-checkboxes">
          <div *ngFor="let conseil of conseils" class="checkbox-item">
            <input
              type="checkbox"
              [id]="'user-' + user.id + '-conseil-' + conseil.id"
              [checked]="isUserAssignedToConseil(conseil.id, user.id)"
              (change)="toggleUserAssignment(conseil.id, user.id)">
            <label [for]="'user-' + user.id + '-conseil-' + conseil.id" class="checkbox-label">
              {{ conseil.classes }}
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- ✅ Bouton de validation -->
  <div class="text-center mt-4">
    <button class="btn btn-success" (click)="saveParticipantsAssignments()">✅ Valider les affectations</button>
  </div>
</div>

    </div>
  </div>

  <!-- Calendar Modal -->
  <div class="calendar-modal" [ngClass]="{'show': showCalendarModal}" (click)="closeCalendarModal()">
    <div class="calendar-modal-content" (click)="$event.stopPropagation()">
      <div class="calendar-header">
        <h3>Calendrier Complet des Conseils</h3>
        <button class="close-btn" (click)="closeCalendarModal()">&times;</button>
      </div>

      <div class="calendar-navigation">
        <button class="nav-btn" (click)="previousMonth()">‹</button>
        <h4>{{ calendarMonths[currentCalendarDate.getMonth()] }} {{ currentCalendarDate.getFullYear() }}</h4>
        <button class="nav-btn" (click)="nextMonth()">›</button>
        <button class="today-btn" (click)="goToToday()">Aujourd'hui</button>
      </div>

      <div class="calendar-grid">
        <div class="calendar-weekdays">
          <div class="weekday">Lun</div>
          <div class="weekday">Mar</div>
          <div class="weekday">Mer</div>
          <div class="weekday">Jeu</div>
          <div class="weekday">Ven</div>
          <div class="weekday">Sam</div>
          <div class="weekday">Dim</div>
        </div>

        <div class="calendar-days">
          <div *ngFor="let dayInfo of calendarDays"
               class="calendar-day"
               [ngClass]="{
                 'other-month': !dayInfo.isCurrentMonth,
                 'today': dayInfo.isToday,
                 'has-conseil': dayInfo.conseils.length > 0,
                 'selected': selectedDate && dayInfo.date.toDateString() === selectedDate.toDateString()
               }"
               (click)="selectCalendarDate(dayInfo)">
            <span class="day-number">{{ dayInfo.day }}</span>
            <div class="conseil-indicators" *ngIf="dayInfo.conseils.length > 0">
              <div *ngFor="let conseil of dayInfo.conseils"
                   class="conseil-indicator"
                   [title]="conseil.classes + ' - ' + (conseil.heure || 'Heure non définie')">
                {{ conseil.classes }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="calendar-legend">
        <div class="legend-item">
          <div class="legend-color today-color"></div>
          <span>Aujourd'hui</span>
        </div>
        <div class="legend-item">
          <div class="legend-color conseil-color"></div>
          <span>Jour avec conseil</span>
        </div>
        <div class="legend-item">
          <div class="legend-color selected-color"></div>
          <span>Jour sélectionné</span>
        </div>
      </div>
    </div>
  </div>

<!-- Participants Display Modal -->
<div class="participants-display-modal-overlay" *ngIf="showParticipantsDisplayModal" (click)="closeParticipantsDisplayModal()">
  <div class="participants-display-modal" (click)="$event.stopPropagation()">
    <div class="participants-display-modal-header">
      <h3 class="participants-display-modal-title">
        Participants - {{ selectedConseilForParticipants?.classes }}
      </h3>
      <button class="close-modal-btn" (click)="closeParticipantsDisplayModal()">
        <i class="ti ti-x"></i>
      </button>
    </div>

    <div class="participants-display-list">
      <div *ngFor="let participant of getSelectedConseilParticipants()"
           class="participant-display-item">
        <div class="participant-display-info">
          <div class="participant-display-name">
            {{ getNomUtilisateur(participant.utilisateurId) }}
          </div>
          <div class="participant-display-status">
            <span class="status-display-badge"
                  [ngClass]="getParticipantStatus(participant)">
              {{ getStatusText(getParticipantStatus(participant)) }}
            </span>
          </div>
          <div class="participant-display-justification"
               *ngIf="participant.justifictaion && participant.justifictaion.trim() !== ''">
            <strong>Justification:</strong> {{ participant.justifictaion }}
          </div>
          <div class="participant-display-justification no-justification"
               *ngIf="!participant.justifictaion || participant.justifictaion.trim() === ''">
            Aucune justification fournie
          </div>
        </div>
      </div>

      <div *ngIf="!hasParticipants()" class="no-participants-display">
        <p>Aucun participant assigné à ce conseil.</p>
      </div>
    </div>
  </div>

  </div> <!-- Fermeture de conseil-content -->
</app-layout>
