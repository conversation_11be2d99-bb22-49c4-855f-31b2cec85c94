{"version": 3, "sources": ["../../../../../node_modules/@stomp/stompjs/esm6/augment-websocket.js", "../../../../../node_modules/@stomp/stompjs/esm6/byte.js", "../../../../../node_modules/@stomp/stompjs/esm6/frame-impl.js", "../../../../../node_modules/@stomp/stompjs/esm6/parser.js", "../../../../../node_modules/@stomp/stompjs/esm6/types.js", "../../../../../node_modules/@stomp/stompjs/esm6/ticker.js", "../../../../../node_modules/@stomp/stompjs/esm6/versions.js", "../../../../../node_modules/@stomp/stompjs/esm6/stomp-handler.js", "../../../../../node_modules/@stomp/stompjs/esm6/client.js", "../../../../../node_modules/@stomp/stompjs/esm6/stomp-config.js", "../../../../../node_modules/@stomp/stompjs/esm6/stomp-headers.js", "../../../../../node_modules/@stomp/stompjs/esm6/compatibility/heartbeat-info.js", "../../../../../node_modules/@stomp/stompjs/esm6/compatibility/compat-client.js", "../../../../../node_modules/@stomp/stompjs/esm6/compatibility/stomp.js"], "sourcesContent": ["/**\n * @internal\n */\nexport function augmentWebsocket(webSocket, debug) {\n    webSocket.terminate = function () {\n        const noOp = () => { };\n        // set all callbacks to no op\n        this.onerror = noOp;\n        this.onmessage = noOp;\n        this.onopen = noOp;\n        const ts = new Date();\n        const id = Math.random().toString().substring(2, 8); // A simulated id\n        const origOnClose = this.onclose;\n        // Track delay in actual closure of the socket\n        this.onclose = closeEvent => {\n            const delay = new Date().getTime() - ts.getTime();\n            debug(`Discarded socket (#${id})  closed after ${delay}ms, with code/reason: ${closeEvent.code}/${closeEvent.reason}`);\n        };\n        this.close();\n        origOnClose?.call(webSocket, {\n            code: 4001,\n            reason: `Quick discarding socket (#${id}) without waiting for the shutdown sequence.`,\n            wasClean: false,\n        });\n    };\n}\n", "/**\n * Some byte values, used as per STOMP specifications.\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport const BYTE = {\n    // LINEFEED byte (octet 10)\n    LF: '\\x0A',\n    // NULL byte (octet 0)\n    NULL: '\\x00',\n};\n", "import { BYTE } from './byte.js';\n/**\n * Frame class represents a STOMP frame.\n *\n * @internal\n */\nexport class FrameImpl {\n    /**\n     * body of the frame\n     */\n    get body() {\n        if (!this._body && this.isBinaryBody) {\n            this._body = new TextDecoder().decode(this._binaryBody);\n        }\n        return this._body || '';\n    }\n    /**\n     * body as Uint8Array\n     */\n    get binaryBody() {\n        if (!this._binaryBody && !this.isBinaryBody) {\n            this._binaryBody = new TextEncoder().encode(this._body);\n        }\n        // At this stage it will definitely have a valid value\n        return this._binaryBody;\n    }\n    /**\n     * Frame constructor. `command`, `headers` and `body` are available as properties.\n     *\n     * @internal\n     */\n    constructor(params) {\n        const { command, headers, body, binaryBody, escapeHeaderValues, skipContentLengthHeader, } = params;\n        this.command = command;\n        this.headers = Object.assign({}, headers || {});\n        if (binaryBody) {\n            this._binaryBody = binaryBody;\n            this.isBinaryBody = true;\n        }\n        else {\n            this._body = body || '';\n            this.isBinaryBody = false;\n        }\n        this.escapeHeaderValues = escapeHeaderValues || false;\n        this.skipContentLengthHeader = skipContentLengthHeader || false;\n    }\n    /**\n     * deserialize a STOMP Frame from raw data.\n     *\n     * @internal\n     */\n    static fromRawFrame(rawFrame, escapeHeaderValues) {\n        const headers = {};\n        const trim = (str) => str.replace(/^\\s+|\\s+$/g, '');\n        // In case of repeated headers, as per standards, first value need to be used\n        for (const header of rawFrame.headers.reverse()) {\n            const idx = header.indexOf(':');\n            const key = trim(header[0]);\n            let value = trim(header[1]);\n            if (escapeHeaderValues &&\n                rawFrame.command !== 'CONNECT' &&\n                rawFrame.command !== 'CONNECTED') {\n                value = FrameImpl.hdrValueUnEscape(value);\n            }\n            headers[key] = value;\n        }\n        return new FrameImpl({\n            command: rawFrame.command,\n            headers,\n            binaryBody: rawFrame.binaryBody,\n            escapeHeaderValues,\n        });\n    }\n    /**\n     * @internal\n     */\n    toString() {\n        return this.serializeCmdAndHeaders();\n    }\n    /**\n     * serialize this Frame in a format suitable to be passed to WebSocket.\n     * If the body is string the output will be string.\n     * If the body is binary (i.e. of type Unit8Array) it will be serialized to ArrayBuffer.\n     *\n     * @internal\n     */\n    serialize() {\n        const cmdAndHeaders = this.serializeCmdAndHeaders();\n        if (this.isBinaryBody) {\n            return FrameImpl.toUnit8Array(cmdAndHeaders, this._binaryBody).buffer;\n        }\n        else {\n            return cmdAndHeaders + this._body + BYTE.NULL;\n        }\n    }\n    serializeCmdAndHeaders() {\n        const lines = [this.command];\n        if (this.skipContentLengthHeader) {\n            delete this.headers['content-length'];\n        }\n        for (const name of Object.keys(this.headers || {})) {\n            const value = this.headers[name];\n            if (this.escapeHeaderValues &&\n                this.command !== 'CONNECT' &&\n                this.command !== 'CONNECTED') {\n                lines.push(`${name}:${FrameImpl.hdrValueEscape(`${value}`)}`);\n            }\n            else {\n                lines.push(`${name}:${value}`);\n            }\n        }\n        if (this.isBinaryBody ||\n            (!this.isBodyEmpty() && !this.skipContentLengthHeader)) {\n            lines.push(`content-length:${this.bodyLength()}`);\n        }\n        return lines.join(BYTE.LF) + BYTE.LF + BYTE.LF;\n    }\n    isBodyEmpty() {\n        return this.bodyLength() === 0;\n    }\n    bodyLength() {\n        const binaryBody = this.binaryBody;\n        return binaryBody ? binaryBody.length : 0;\n    }\n    /**\n     * Compute the size of a UTF-8 string by counting its number of bytes\n     * (and not the number of characters composing the string)\n     */\n    static sizeOfUTF8(s) {\n        return s ? new TextEncoder().encode(s).length : 0;\n    }\n    static toUnit8Array(cmdAndHeaders, binaryBody) {\n        const uint8CmdAndHeaders = new TextEncoder().encode(cmdAndHeaders);\n        const nullTerminator = new Uint8Array([0]);\n        const uint8Frame = new Uint8Array(uint8CmdAndHeaders.length + binaryBody.length + nullTerminator.length);\n        uint8Frame.set(uint8CmdAndHeaders);\n        uint8Frame.set(binaryBody, uint8CmdAndHeaders.length);\n        uint8Frame.set(nullTerminator, uint8CmdAndHeaders.length + binaryBody.length);\n        return uint8Frame;\n    }\n    /**\n     * Serialize a STOMP frame as per STOMP standards, suitable to be sent to the STOMP broker.\n     *\n     * @internal\n     */\n    static marshall(params) {\n        const frame = new FrameImpl(params);\n        return frame.serialize();\n    }\n    /**\n     *  Escape header values\n     */\n    static hdrValueEscape(str) {\n        return str\n            .replace(/\\\\/g, '\\\\\\\\')\n            .replace(/\\r/g, '\\\\r')\n            .replace(/\\n/g, '\\\\n')\n            .replace(/:/g, '\\\\c');\n    }\n    /**\n     * UnEscape header values\n     */\n    static hdrValueUnEscape(str) {\n        return str\n            .replace(/\\\\r/g, '\\r')\n            .replace(/\\\\n/g, '\\n')\n            .replace(/\\\\c/g, ':')\n            .replace(/\\\\\\\\/g, '\\\\');\n    }\n}\n", "/**\n * @internal\n */\nconst NULL = 0;\n/**\n * @internal\n */\nconst LF = 10;\n/**\n * @internal\n */\nconst CR = 13;\n/**\n * @internal\n */\nconst COLON = 58;\n/**\n * This is an evented, rec descent parser.\n * A stream of Octets can be passed and whenever it recognizes\n * a complete Frame or an incoming ping it will invoke the registered callbacks.\n *\n * All incoming Octets are fed into _onByte function.\n * Depending on current state the _onByte function keeps changing.\n * Depending on the state it keeps accumulating into _token and _results.\n * State is indicated by current value of _onByte, all states are named as _collect.\n *\n * STOMP standards https://stomp.github.io/stomp-specification-1.2.html\n * imply that all lengths are considered in bytes (instead of string lengths).\n * So, before actual parsing, if the incoming data is String it is converted to Octets.\n * This allows faithful implementation of the protocol and allows NULL Octets to be present in the body.\n *\n * There is no peek function on the incoming data.\n * When a state change occurs based on an Octet without consuming the Octet,\n * the Octet, after state change, is fed again (_reinjectByte).\n * This became possible as the state change can be determined by inspecting just one Octet.\n *\n * There are two modes to collect the body, if content-length header is there then it by counting Octets\n * otherwise it is determined by NULL terminator.\n *\n * Following the standards, the command and headers are converted to Strings\n * and the body is returned as Octets.\n * Headers are returned as an array and not as Hash - to allow multiple occurrence of an header.\n *\n * This parser does not use Regular Expressions as that can only operate on Strings.\n *\n * It handles if multiple STOMP frames are given as one chunk, a frame is split into multiple chunks, or\n * any combination there of. The parser remembers its state (any partial frame) and continues when a new chunk\n * is pushed.\n *\n * Typically the higher level function will convert headers to Hash, handle unescaping of header values\n * (which is protocol version specific), and convert body to text.\n *\n * Check the parser.spec.js to understand cases that this parser is supposed to handle.\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class Parser {\n    constructor(onFrame, onIncomingPing) {\n        this.onFrame = onFrame;\n        this.onIncomingPing = onIncomingPing;\n        this._encoder = new TextEncoder();\n        this._decoder = new TextDecoder();\n        this._token = [];\n        this._initState();\n    }\n    parseChunk(segment, appendMissingNULLonIncoming = false) {\n        let chunk;\n        if (typeof segment === 'string') {\n            chunk = this._encoder.encode(segment);\n        }\n        else {\n            chunk = new Uint8Array(segment);\n        }\n        // See https://github.com/stomp-js/stompjs/issues/89\n        // Remove when underlying issue is fixed.\n        //\n        // Send a NULL byte, if the last byte of a Text frame was not NULL.F\n        if (appendMissingNULLonIncoming && chunk[chunk.length - 1] !== 0) {\n            const chunkWithNull = new Uint8Array(chunk.length + 1);\n            chunkWithNull.set(chunk, 0);\n            chunkWithNull[chunk.length] = 0;\n            chunk = chunkWithNull;\n        }\n        // tslint:disable-next-line:prefer-for-of\n        for (let i = 0; i < chunk.length; i++) {\n            const byte = chunk[i];\n            this._onByte(byte);\n        }\n    }\n    // The following implements a simple Rec Descent Parser.\n    // The grammar is simple and just one byte tells what should be the next state\n    _collectFrame(byte) {\n        if (byte === NULL) {\n            // Ignore\n            return;\n        }\n        if (byte === CR) {\n            // Ignore CR\n            return;\n        }\n        if (byte === LF) {\n            // Incoming Ping\n            this.onIncomingPing();\n            return;\n        }\n        this._onByte = this._collectCommand;\n        this._reinjectByte(byte);\n    }\n    _collectCommand(byte) {\n        if (byte === CR) {\n            // Ignore CR\n            return;\n        }\n        if (byte === LF) {\n            this._results.command = this._consumeTokenAsUTF8();\n            this._onByte = this._collectHeaders;\n            return;\n        }\n        this._consumeByte(byte);\n    }\n    _collectHeaders(byte) {\n        if (byte === CR) {\n            // Ignore CR\n            return;\n        }\n        if (byte === LF) {\n            this._setupCollectBody();\n            return;\n        }\n        this._onByte = this._collectHeaderKey;\n        this._reinjectByte(byte);\n    }\n    _reinjectByte(byte) {\n        this._onByte(byte);\n    }\n    _collectHeaderKey(byte) {\n        if (byte === COLON) {\n            this._headerKey = this._consumeTokenAsUTF8();\n            this._onByte = this._collectHeaderValue;\n            return;\n        }\n        this._consumeByte(byte);\n    }\n    _collectHeaderValue(byte) {\n        if (byte === CR) {\n            // Ignore CR\n            return;\n        }\n        if (byte === LF) {\n            this._results.headers.push([\n                this._headerKey,\n                this._consumeTokenAsUTF8(),\n            ]);\n            this._headerKey = undefined;\n            this._onByte = this._collectHeaders;\n            return;\n        }\n        this._consumeByte(byte);\n    }\n    _setupCollectBody() {\n        const contentLengthHeader = this._results.headers.filter((header) => {\n            return header[0] === 'content-length';\n        })[0];\n        if (contentLengthHeader) {\n            this._bodyBytesRemaining = parseInt(contentLengthHeader[1], 10);\n            this._onByte = this._collectBodyFixedSize;\n        }\n        else {\n            this._onByte = this._collectBodyNullTerminated;\n        }\n    }\n    _collectBodyNullTerminated(byte) {\n        if (byte === NULL) {\n            this._retrievedBody();\n            return;\n        }\n        this._consumeByte(byte);\n    }\n    _collectBodyFixedSize(byte) {\n        // It is post decrement, so that we discard the trailing NULL octet\n        if (this._bodyBytesRemaining-- === 0) {\n            this._retrievedBody();\n            return;\n        }\n        this._consumeByte(byte);\n    }\n    _retrievedBody() {\n        this._results.binaryBody = this._consumeTokenAsRaw();\n        try {\n            this.onFrame(this._results);\n        }\n        catch (e) {\n            console.log(`Ignoring an exception thrown by a frame handler. Original exception: `, e);\n        }\n        this._initState();\n    }\n    // Rec Descent Parser helpers\n    _consumeByte(byte) {\n        this._token.push(byte);\n    }\n    _consumeTokenAsUTF8() {\n        return this._decoder.decode(this._consumeTokenAsRaw());\n    }\n    _consumeTokenAsRaw() {\n        const rawResult = new Uint8Array(this._token);\n        this._token = [];\n        return rawResult;\n    }\n    _initState() {\n        this._results = {\n            command: undefined,\n            headers: [],\n            binaryBody: undefined,\n        };\n        this._token = [];\n        this._headerKey = undefined;\n        this._onByte = this._collectFrame;\n    }\n}\n", "/**\n * Possible states for the IStompSocket\n */\nexport var StompSocketState;\n(function (StompSocketState) {\n    StompSocketState[StompSocketState[\"CONNECTING\"] = 0] = \"CONNECTING\";\n    StompSocketState[StompSocketState[\"OPEN\"] = 1] = \"OPEN\";\n    StompSocketState[StompSocketState[\"CLOSING\"] = 2] = \"CLOSING\";\n    StompSocketState[StompSocketState[\"CLOSED\"] = 3] = \"CLOSED\";\n})(StompSocketState || (StompSocketState = {}));\n/**\n * Possible activation state\n */\nexport var ActivationState;\n(function (ActivationState) {\n    ActivationState[ActivationState[\"ACTIVE\"] = 0] = \"ACTIVE\";\n    ActivationState[ActivationState[\"DEACTIVATING\"] = 1] = \"DEACTIVATING\";\n    ActivationState[ActivationState[\"INACTIVE\"] = 2] = \"INACTIVE\";\n})(ActivationState || (ActivationState = {}));\n/**\n * Possible reconnection wait time modes\n */\nexport var ReconnectionTimeMode;\n(function (ReconnectionTimeMode) {\n    ReconnectionTimeMode[ReconnectionTimeMode[\"LINEAR\"] = 0] = \"LINEAR\";\n    ReconnectionTimeMode[ReconnectionTimeMode[\"EXPONENTIAL\"] = 1] = \"EXPONENTIAL\";\n})(ReconnectionTimeMode || (ReconnectionTimeMode = {}));\n/**\n * Possible ticker strategies for outgoing heartbeat ping\n */\nexport var TickerStrategy;\n(function (TickerStrategy) {\n    TickerStrategy[\"Interval\"] = \"interval\";\n    TickerStrategy[\"Worker\"] = \"worker\";\n})(TickerStrategy || (TickerStrategy = {}));\n", "import { TickerStrategy } from './types.js';\nexport class Ticker {\n    constructor(_interval, _strategy = TickerStrategy.Interval, _debug) {\n        this._interval = _interval;\n        this._strategy = _strategy;\n        this._debug = _debug;\n        this._workerScript = `\n    var startTime = Date.now();\n    setInterval(function() {\n        self.postMessage(Date.now() - startTime);\n    }, ${this._interval});\n  `;\n    }\n    start(tick) {\n        this.stop();\n        if (this.shouldUseWorker()) {\n            this.runWorker(tick);\n        }\n        else {\n            this.runInterval(tick);\n        }\n    }\n    stop() {\n        this.disposeWorker();\n        this.disposeInterval();\n    }\n    shouldUseWorker() {\n        return typeof (Worker) !== 'undefined' && this._strategy === TickerStrategy.Worker;\n    }\n    runWorker(tick) {\n        this._debug('Using runWorker for outgoing pings');\n        if (!this._worker) {\n            this._worker = new Worker(URL.createObjectURL(new Blob([this._workerScript], { type: 'text/javascript' })));\n            this._worker.onmessage = (message) => tick(message.data);\n        }\n    }\n    runInterval(tick) {\n        this._debug('Using runInterval for outgoing pings');\n        if (!this._timer) {\n            const startTime = Date.now();\n            this._timer = setInterval(() => {\n                tick(Date.now() - startTime);\n            }, this._interval);\n        }\n    }\n    disposeWorker() {\n        if (this._worker) {\n            this._worker.terminate();\n            delete this._worker;\n            this._debug('Outgoing ping disposeWorker');\n        }\n    }\n    disposeInterval() {\n        if (this._timer) {\n            clearInterval(this._timer);\n            delete this._timer;\n            this._debug('Outgoing ping disposeInterval');\n        }\n    }\n}\n", "/**\n * Supported STOMP versions\n *\n * Part of `@stomp/stompjs`.\n */\nexport class Versions {\n    /**\n     * Takes an array of versions, typical elements '1.2', '1.1', or '1.0'\n     *\n     * You will be creating an instance of this class if you want to override\n     * supported versions to be declared during STOMP handshake.\n     */\n    constructor(versions) {\n        this.versions = versions;\n    }\n    /**\n     * Used as part of CONNECT STOMP Frame\n     */\n    supportedVersions() {\n        return this.versions.join(',');\n    }\n    /**\n     * Used while creating a WebSocket\n     */\n    protocolVersions() {\n        return this.versions.map(x => `v${x.replace('.', '')}.stomp`);\n    }\n}\n/**\n * Indicates protocol version 1.0\n */\nVersions.V1_0 = '1.0';\n/**\n * Indicates protocol version 1.1\n */\nVersions.V1_1 = '1.1';\n/**\n * Indicates protocol version 1.2\n */\nVersions.V1_2 = '1.2';\n/**\n * @internal\n */\nVersions.default = new Versions([\n    Versions.V1_2,\n    Versions.V1_1,\n    Versions.V1_0,\n]);\n", "import { augmentWebsocket } from './augment-websocket.js';\nimport { BYTE } from './byte.js';\nimport { FrameImpl } from './frame-impl.js';\nimport { Parser } from './parser.js';\nimport { Ticker } from './ticker.js';\nimport { StompSocketState, } from './types.js';\nimport { Versions } from './versions.js';\n/**\n * The STOMP protocol handler\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class StompHandler {\n    get connectedVersion() {\n        return this._connectedVersion;\n    }\n    get connected() {\n        return this._connected;\n    }\n    constructor(_client, _webSocket, config) {\n        this._client = _client;\n        this._webSocket = _webSocket;\n        this._connected = false;\n        this._serverFrameHandlers = {\n            // [CONNECTED Frame](https://stomp.github.com/stomp-specification-1.2.html#CONNECTED_Frame)\n            CONNECTED: frame => {\n                this.debug(`connected to server ${frame.headers.server}`);\n                this._connected = true;\n                this._connectedVersion = frame.headers.version;\n                // STOMP version 1.2 needs header values to be escaped\n                if (this._connectedVersion === Versions.V1_2) {\n                    this._escapeHeaderValues = true;\n                }\n                this._setupHeartbeat(frame.headers);\n                this.onConnect(frame);\n            },\n            // [MESSAGE Frame](https://stomp.github.com/stomp-specification-1.2.html#MESSAGE)\n            MESSAGE: frame => {\n                // the callback is registered when the client calls\n                // `subscribe()`.\n                // If there is no registered subscription for the received message,\n                // the default `onUnhandledMessage` callback is used that the client can set.\n                // This is useful for subscriptions that are automatically created\n                // on the browser side (e.g. [RabbitMQ's temporary\n                // queues](https://www.rabbitmq.com/stomp.html)).\n                const subscription = frame.headers.subscription;\n                const onReceive = this._subscriptions[subscription] || this.onUnhandledMessage;\n                // bless the frame to be a Message\n                const message = frame;\n                const client = this;\n                const messageId = this._connectedVersion === Versions.V1_2\n                    ? message.headers.ack\n                    : message.headers['message-id'];\n                // add `ack()` and `nack()` methods directly to the returned frame\n                // so that a simple call to `message.ack()` can acknowledge the message.\n                message.ack = (headers = {}) => {\n                    return client.ack(messageId, subscription, headers);\n                };\n                message.nack = (headers = {}) => {\n                    return client.nack(messageId, subscription, headers);\n                };\n                onReceive(message);\n            },\n            // [RECEIPT Frame](https://stomp.github.com/stomp-specification-1.2.html#RECEIPT)\n            RECEIPT: frame => {\n                const callback = this._receiptWatchers[frame.headers['receipt-id']];\n                if (callback) {\n                    callback(frame);\n                    // Server will acknowledge only once, remove the callback\n                    delete this._receiptWatchers[frame.headers['receipt-id']];\n                }\n                else {\n                    this.onUnhandledReceipt(frame);\n                }\n            },\n            // [ERROR Frame](https://stomp.github.com/stomp-specification-1.2.html#ERROR)\n            ERROR: frame => {\n                this.onStompError(frame);\n            },\n        };\n        // used to index subscribers\n        this._counter = 0;\n        // subscription callbacks indexed by subscriber's ID\n        this._subscriptions = {};\n        // receipt-watchers indexed by receipts-ids\n        this._receiptWatchers = {};\n        this._partialData = '';\n        this._escapeHeaderValues = false;\n        this._lastServerActivityTS = Date.now();\n        this.debug = config.debug;\n        this.stompVersions = config.stompVersions;\n        this.connectHeaders = config.connectHeaders;\n        this.disconnectHeaders = config.disconnectHeaders;\n        this.heartbeatIncoming = config.heartbeatIncoming;\n        this.heartbeatOutgoing = config.heartbeatOutgoing;\n        this.splitLargeFrames = config.splitLargeFrames;\n        this.maxWebSocketChunkSize = config.maxWebSocketChunkSize;\n        this.forceBinaryWSFrames = config.forceBinaryWSFrames;\n        this.logRawCommunication = config.logRawCommunication;\n        this.appendMissingNULLonIncoming = config.appendMissingNULLonIncoming;\n        this.discardWebsocketOnCommFailure = config.discardWebsocketOnCommFailure;\n        this.onConnect = config.onConnect;\n        this.onDisconnect = config.onDisconnect;\n        this.onStompError = config.onStompError;\n        this.onWebSocketClose = config.onWebSocketClose;\n        this.onWebSocketError = config.onWebSocketError;\n        this.onUnhandledMessage = config.onUnhandledMessage;\n        this.onUnhandledReceipt = config.onUnhandledReceipt;\n        this.onUnhandledFrame = config.onUnhandledFrame;\n    }\n    start() {\n        const parser = new Parser(\n        // On Frame\n        rawFrame => {\n            const frame = FrameImpl.fromRawFrame(rawFrame, this._escapeHeaderValues);\n            // if this.logRawCommunication is set, the rawChunk is logged at this._webSocket.onmessage\n            if (!this.logRawCommunication) {\n                this.debug(`<<< ${frame}`);\n            }\n            const serverFrameHandler = this._serverFrameHandlers[frame.command] || this.onUnhandledFrame;\n            serverFrameHandler(frame);\n        }, \n        // On Incoming Ping\n        () => {\n            this.debug('<<< PONG');\n        });\n        this._webSocket.onmessage = (evt) => {\n            this.debug('Received data');\n            this._lastServerActivityTS = Date.now();\n            if (this.logRawCommunication) {\n                const rawChunkAsString = evt.data instanceof ArrayBuffer\n                    ? new TextDecoder().decode(evt.data)\n                    : evt.data;\n                this.debug(`<<< ${rawChunkAsString}`);\n            }\n            parser.parseChunk(evt.data, this.appendMissingNULLonIncoming);\n        };\n        this._webSocket.onclose = (closeEvent) => {\n            this.debug(`Connection closed to ${this._webSocket.url}`);\n            this._cleanUp();\n            this.onWebSocketClose(closeEvent);\n        };\n        this._webSocket.onerror = (errorEvent) => {\n            this.onWebSocketError(errorEvent);\n        };\n        this._webSocket.onopen = () => {\n            // Clone before updating\n            const connectHeaders = Object.assign({}, this.connectHeaders);\n            this.debug('Web Socket Opened...');\n            connectHeaders['accept-version'] = this.stompVersions.supportedVersions();\n            connectHeaders['heart-beat'] = [\n                this.heartbeatOutgoing,\n                this.heartbeatIncoming,\n            ].join(',');\n            this._transmit({ command: 'CONNECT', headers: connectHeaders });\n        };\n    }\n    _setupHeartbeat(headers) {\n        if (headers.version !== Versions.V1_1 &&\n            headers.version !== Versions.V1_2) {\n            return;\n        }\n        // It is valid for the server to not send this header\n        // https://stomp.github.io/stomp-specification-1.2.html#Heart-beating\n        if (!headers['heart-beat']) {\n            return;\n        }\n        // heart-beat header received from the server looks like:\n        //\n        //     heart-beat: sx, sy\n        const [serverOutgoing, serverIncoming] = headers['heart-beat']\n            .split(',')\n            .map((v) => parseInt(v, 10));\n        if (this.heartbeatOutgoing !== 0 && serverIncoming !== 0) {\n            const ttl = Math.max(this.heartbeatOutgoing, serverIncoming);\n            this.debug(`send PING every ${ttl}ms`);\n            this._pinger = new Ticker(ttl, this._client.heartbeatStrategy, this.debug);\n            this._pinger.start(() => {\n                if (this._webSocket.readyState === StompSocketState.OPEN) {\n                    this._webSocket.send(BYTE.LF);\n                    this.debug('>>> PING');\n                }\n            });\n        }\n        if (this.heartbeatIncoming !== 0 && serverOutgoing !== 0) {\n            const ttl = Math.max(this.heartbeatIncoming, serverOutgoing);\n            this.debug(`check PONG every ${ttl}ms`);\n            this._ponger = setInterval(() => {\n                const delta = Date.now() - this._lastServerActivityTS;\n                // We wait twice the TTL to be flexible on window's setInterval calls\n                if (delta > ttl * 2) {\n                    this.debug(`did not receive server activity for the last ${delta}ms`);\n                    this._closeOrDiscardWebsocket();\n                }\n            }, ttl);\n        }\n    }\n    _closeOrDiscardWebsocket() {\n        if (this.discardWebsocketOnCommFailure) {\n            this.debug('Discarding websocket, the underlying socket may linger for a while');\n            this.discardWebsocket();\n        }\n        else {\n            this.debug('Issuing close on the websocket');\n            this._closeWebsocket();\n        }\n    }\n    forceDisconnect() {\n        if (this._webSocket) {\n            if (this._webSocket.readyState === StompSocketState.CONNECTING ||\n                this._webSocket.readyState === StompSocketState.OPEN) {\n                this._closeOrDiscardWebsocket();\n            }\n        }\n    }\n    _closeWebsocket() {\n        this._webSocket.onmessage = () => { }; // ignore messages\n        this._webSocket.close();\n    }\n    discardWebsocket() {\n        if (typeof this._webSocket.terminate !== 'function') {\n            augmentWebsocket(this._webSocket, (msg) => this.debug(msg));\n        }\n        // @ts-ignore - this method will be there at this stage\n        this._webSocket.terminate();\n    }\n    _transmit(params) {\n        const { command, headers, body, binaryBody, skipContentLengthHeader } = params;\n        const frame = new FrameImpl({\n            command,\n            headers,\n            body,\n            binaryBody,\n            escapeHeaderValues: this._escapeHeaderValues,\n            skipContentLengthHeader,\n        });\n        let rawChunk = frame.serialize();\n        if (this.logRawCommunication) {\n            this.debug(`>>> ${rawChunk}`);\n        }\n        else {\n            this.debug(`>>> ${frame}`);\n        }\n        if (this.forceBinaryWSFrames && typeof rawChunk === 'string') {\n            rawChunk = new TextEncoder().encode(rawChunk);\n        }\n        if (typeof rawChunk !== 'string' || !this.splitLargeFrames) {\n            this._webSocket.send(rawChunk);\n        }\n        else {\n            let out = rawChunk;\n            while (out.length > 0) {\n                const chunk = out.substring(0, this.maxWebSocketChunkSize);\n                out = out.substring(this.maxWebSocketChunkSize);\n                this._webSocket.send(chunk);\n                this.debug(`chunk sent = ${chunk.length}, remaining = ${out.length}`);\n            }\n        }\n    }\n    dispose() {\n        if (this.connected) {\n            try {\n                // clone before updating\n                const disconnectHeaders = Object.assign({}, this.disconnectHeaders);\n                if (!disconnectHeaders.receipt) {\n                    disconnectHeaders.receipt = `close-${this._counter++}`;\n                }\n                this.watchForReceipt(disconnectHeaders.receipt, frame => {\n                    this._closeWebsocket();\n                    this._cleanUp();\n                    this.onDisconnect(frame);\n                });\n                this._transmit({ command: 'DISCONNECT', headers: disconnectHeaders });\n            }\n            catch (error) {\n                this.debug(`Ignoring error during disconnect ${error}`);\n            }\n        }\n        else {\n            if (this._webSocket.readyState === StompSocketState.CONNECTING ||\n                this._webSocket.readyState === StompSocketState.OPEN) {\n                this._closeWebsocket();\n            }\n        }\n    }\n    _cleanUp() {\n        this._connected = false;\n        if (this._pinger) {\n            this._pinger.stop();\n            this._pinger = undefined;\n        }\n        if (this._ponger) {\n            clearInterval(this._ponger);\n            this._ponger = undefined;\n        }\n    }\n    publish(params) {\n        const { destination, headers, body, binaryBody, skipContentLengthHeader } = params;\n        const hdrs = Object.assign({ destination }, headers);\n        this._transmit({\n            command: 'SEND',\n            headers: hdrs,\n            body,\n            binaryBody,\n            skipContentLengthHeader,\n        });\n    }\n    watchForReceipt(receiptId, callback) {\n        this._receiptWatchers[receiptId] = callback;\n    }\n    subscribe(destination, callback, headers = {}) {\n        headers = Object.assign({}, headers);\n        if (!headers.id) {\n            headers.id = `sub-${this._counter++}`;\n        }\n        headers.destination = destination;\n        this._subscriptions[headers.id] = callback;\n        this._transmit({ command: 'SUBSCRIBE', headers });\n        const client = this;\n        return {\n            id: headers.id,\n            unsubscribe(hdrs) {\n                return client.unsubscribe(headers.id, hdrs);\n            },\n        };\n    }\n    unsubscribe(id, headers = {}) {\n        headers = Object.assign({}, headers);\n        delete this._subscriptions[id];\n        headers.id = id;\n        this._transmit({ command: 'UNSUBSCRIBE', headers });\n    }\n    begin(transactionId) {\n        const txId = transactionId || `tx-${this._counter++}`;\n        this._transmit({\n            command: 'BEGIN',\n            headers: {\n                transaction: txId,\n            },\n        });\n        const client = this;\n        return {\n            id: txId,\n            commit() {\n                client.commit(txId);\n            },\n            abort() {\n                client.abort(txId);\n            },\n        };\n    }\n    commit(transactionId) {\n        this._transmit({\n            command: 'COMMIT',\n            headers: {\n                transaction: transactionId,\n            },\n        });\n    }\n    abort(transactionId) {\n        this._transmit({\n            command: 'ABORT',\n            headers: {\n                transaction: transactionId,\n            },\n        });\n    }\n    ack(messageId, subscriptionId, headers = {}) {\n        headers = Object.assign({}, headers);\n        if (this._connectedVersion === Versions.V1_2) {\n            headers.id = messageId;\n        }\n        else {\n            headers['message-id'] = messageId;\n        }\n        headers.subscription = subscriptionId;\n        this._transmit({ command: 'ACK', headers });\n    }\n    nack(messageId, subscriptionId, headers = {}) {\n        headers = Object.assign({}, headers);\n        if (this._connectedVersion === Versions.V1_2) {\n            headers.id = messageId;\n        }\n        else {\n            headers['message-id'] = messageId;\n        }\n        headers.subscription = subscriptionId;\n        return this._transmit({ command: 'NACK', headers });\n    }\n}\n", "import { <PERSON>om<PERSON><PERSON>and<PERSON> } from './stomp-handler.js';\nimport { ActivationState, ReconnectionTimeMode, StompSocketState, TickerStrategy, } from './types.js';\nimport { Versions } from './versions.js';\n/**\n * STOMP Client Class.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class Client {\n    /**\n     * Underlying WebSocket instance, READONLY.\n     */\n    get webSocket() {\n        return this._stompHandler?._webSocket;\n    }\n    /**\n     * Disconnection headers.\n     */\n    get disconnectHeaders() {\n        return this._disconnectHeaders;\n    }\n    set disconnectHeaders(value) {\n        this._disconnectHeaders = value;\n        if (this._stompHandler) {\n            this._stompHandler.disconnectHeaders = this._disconnectHeaders;\n        }\n    }\n    /**\n     * `true` if there is an active connection to STOMP Broker\n     */\n    get connected() {\n        return !!this._stompHandler && this._stompHandler.connected;\n    }\n    /**\n     * version of STOMP protocol negotiated with the server, READONLY\n     */\n    get connectedVersion() {\n        return this._stompHandler ? this._stompHandler.connectedVersion : undefined;\n    }\n    /**\n     * if the client is active (connected or going to reconnect)\n     */\n    get active() {\n        return this.state === ActivationState.ACTIVE;\n    }\n    _changeState(state) {\n        this.state = state;\n        this.onChangeState(state);\n    }\n    /**\n     * Create an instance.\n     */\n    constructor(conf = {}) {\n        /**\n         * STOMP versions to attempt during STOMP handshake. By default, versions `1.2`, `1.1`, and `1.0` are attempted.\n         *\n         * Example:\n         * ```javascript\n         *        // Try only versions 1.1 and 1.0\n         *        client.stompVersions = new Versions(['1.1', '1.0'])\n         * ```\n         */\n        this.stompVersions = Versions.default;\n        /**\n         * Will retry if Stomp connection is not established in specified milliseconds.\n         * Default 0, which switches off automatic reconnection.\n         */\n        this.connectionTimeout = 0;\n        /**\n         *  automatically reconnect with delay in milliseconds, set to 0 to disable.\n         */\n        this.reconnectDelay = 5000;\n        /**\n         * tracking the time to the next reconnection. Initialized to [Client#reconnectDelay]{@link Client#reconnectDelay}'s value and it may\n         * change depending on the [Client#reconnectTimeMode]{@link Client#reconnectTimeMode} setting\n         */\n        this._nextReconnectDelay = 0;\n        /**\n         * Maximum time to wait between reconnects, in milliseconds. Defaults to 15 minutes.\n         * Only relevant when [Client#reconnectTimeMode]{@link Client#reconnectTimeMode} not LINEAR (e.g., EXPONENTIAL).\n         * Set to 0 for no limit on wait time.\n         */\n        this.maxReconnectDelay = 15 * 60 * 1000; // 15 minutes in ms\n        /**\n         * Reconnection wait time mode, either linear (default) or exponential.\n         * Note: See [Client#maxReconnectDelay]{@link Client#maxReconnectDelay} for setting the maximum delay when exponential\n         *\n         * ```javascript\n         * client.configure({\n         *   reconnectTimeMode: ReconnectionTimeMode.EXPONENTIAL,\n         *   reconnectDelay: 200, // It will wait 200, 400, 800 ms...\n         *   maxReconnectDelay: 10000, // Optional, when provided, it will not wait more that these ms\n         * })\n         * ```\n         */\n        this.reconnectTimeMode = ReconnectionTimeMode.LINEAR;\n        /**\n         * Incoming heartbeat interval in milliseconds. Set to 0 to disable.\n         */\n        this.heartbeatIncoming = 10000;\n        /**\n         * Outgoing heartbeat interval in milliseconds. Set to 0 to disable.\n         */\n        this.heartbeatOutgoing = 10000;\n        /**\n         * Outgoing heartbeat strategy.\n         * See https://github.com/stomp-js/stompjs/pull/579\n         *\n         * Can be worker or interval strategy, but will always use `interval`\n         * if web workers are unavailable, for example, in a non-browser environment.\n         *\n         * Using Web Workers may work better on long-running pages\n         * and mobile apps, as the browser may suspend Timers in the main page.\n         * Try the `Worker` mode if you discover disconnects when the browser tab is in the background.\n         *\n         * When used in a JS environment, use 'worker' or 'interval' as valid values.\n         *\n         * Defaults to `interval` strategy.\n         */\n        this.heartbeatStrategy = TickerStrategy.Interval;\n        /**\n         * This switches on a non-standard behavior while sending WebSocket packets.\n         * It splits larger (text) packets into chunks of [maxWebSocketChunkSize]{@link Client#maxWebSocketChunkSize}.\n         * Only Java Spring brokers seem to support this mode.\n         *\n         * WebSockets, by itself, split large (text) packets,\n         * so it is not needed with a truly compliant STOMP/WebSocket broker.\n         * Setting it for such a broker will cause large messages to fail.\n         *\n         * `false` by default.\n         *\n         * Binary frames are never split.\n         */\n        this.splitLargeFrames = false;\n        /**\n         * See [splitLargeFrames]{@link Client#splitLargeFrames}.\n         * This has no effect if [splitLargeFrames]{@link Client#splitLargeFrames} is `false`.\n         */\n        this.maxWebSocketChunkSize = 8 * 1024;\n        /**\n         * Usually the\n         * [type of WebSocket frame]{@link https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send#Parameters}\n         * is automatically decided by type of the payload.\n         * Default is `false`, which should work with all compliant brokers.\n         *\n         * Set this flag to force binary frames.\n         */\n        this.forceBinaryWSFrames = false;\n        /**\n         * A bug in ReactNative chops a string on occurrence of a NULL.\n         * See issue [https://github.com/stomp-js/stompjs/issues/89]{@link https://github.com/stomp-js/stompjs/issues/89}.\n         * This makes incoming WebSocket messages invalid STOMP packets.\n         * Setting this flag attempts to reverse the damage by appending a NULL.\n         * If the broker splits a large message into multiple WebSocket messages,\n         * this flag will cause data loss and abnormal termination of connection.\n         *\n         * This is not an ideal solution, but a stop gap until the underlying issue is fixed at ReactNative library.\n         */\n        this.appendMissingNULLonIncoming = false;\n        /**\n         * Browsers do not immediately close WebSockets when `.close` is issued.\n         * This may cause reconnection to take a significantly long time in case\n         *  of some types of failures.\n         * In case of incoming heartbeat failure, this experimental flag instructs\n         * the library to discard the socket immediately\n         * (even before it is actually closed).\n         */\n        this.discardWebsocketOnCommFailure = false;\n        /**\n         * Activation state.\n         *\n         * It will usually be ACTIVE or INACTIVE.\n         * When deactivating, it may go from ACTIVE to INACTIVE without entering DEACTIVATING.\n         */\n        this.state = ActivationState.INACTIVE;\n        // No op callbacks\n        const noOp = () => { };\n        this.debug = noOp;\n        this.beforeConnect = noOp;\n        this.onConnect = noOp;\n        this.onDisconnect = noOp;\n        this.onUnhandledMessage = noOp;\n        this.onUnhandledReceipt = noOp;\n        this.onUnhandledFrame = noOp;\n        this.onStompError = noOp;\n        this.onWebSocketClose = noOp;\n        this.onWebSocketError = noOp;\n        this.logRawCommunication = false;\n        this.onChangeState = noOp;\n        // These parameters would typically get proper values before connect is called\n        this.connectHeaders = {};\n        this._disconnectHeaders = {};\n        // Apply configuration\n        this.configure(conf);\n    }\n    /**\n     * Update configuration.\n     */\n    configure(conf) {\n        // bulk assign all properties to this\n        Object.assign(this, conf);\n        // Warn on incorrect maxReconnectDelay settings\n        if (this.maxReconnectDelay > 0 &&\n            this.maxReconnectDelay < this.reconnectDelay) {\n            this.debug(`Warning: maxReconnectDelay (${this.maxReconnectDelay}ms) is less than reconnectDelay (${this.reconnectDelay}ms). Using reconnectDelay as the maxReconnectDelay delay.`);\n            this.maxReconnectDelay = this.reconnectDelay;\n        }\n    }\n    /**\n     * Initiate the connection with the broker.\n     * If the connection breaks, as per [Client#reconnectDelay]{@link Client#reconnectDelay},\n     * it will keep trying to reconnect. If the [Client#reconnectTimeMode]{@link Client#reconnectTimeMode}\n     * is set to EXPONENTIAL it will increase the wait time exponentially\n     *\n     * Call [Client#deactivate]{@link Client#deactivate} to disconnect and stop reconnection attempts.\n     */\n    activate() {\n        const _activate = () => {\n            if (this.active) {\n                this.debug('Already ACTIVE, ignoring request to activate');\n                return;\n            }\n            this._changeState(ActivationState.ACTIVE);\n            this._nextReconnectDelay = this.reconnectDelay;\n            this._connect();\n        };\n        // if it is deactivating, wait for it to complete before activating.\n        if (this.state === ActivationState.DEACTIVATING) {\n            this.debug('Waiting for deactivation to finish before activating');\n            this.deactivate().then(() => {\n                _activate();\n            });\n        }\n        else {\n            _activate();\n        }\n    }\n    async _connect() {\n        await this.beforeConnect(this);\n        if (this._stompHandler) {\n            this.debug('There is already a stompHandler, skipping the call to connect');\n            return;\n        }\n        if (!this.active) {\n            this.debug('Client has been marked inactive, will not attempt to connect');\n            return;\n        }\n        // setup connection watcher\n        if (this.connectionTimeout > 0) {\n            // clear first\n            if (this._connectionWatcher) {\n                clearTimeout(this._connectionWatcher);\n            }\n            this._connectionWatcher = setTimeout(() => {\n                if (this.connected) {\n                    return;\n                }\n                // Connection not established, close the underlying socket\n                // a reconnection will be attempted\n                this.debug(`Connection not established in ${this.connectionTimeout}ms, closing socket`);\n                this.forceDisconnect();\n            }, this.connectionTimeout);\n        }\n        this.debug('Opening Web Socket...');\n        // Get the actual WebSocket (or a similar object)\n        const webSocket = this._createWebSocket();\n        this._stompHandler = new StompHandler(this, webSocket, {\n            debug: this.debug,\n            stompVersions: this.stompVersions,\n            connectHeaders: this.connectHeaders,\n            disconnectHeaders: this._disconnectHeaders,\n            heartbeatIncoming: this.heartbeatIncoming,\n            heartbeatOutgoing: this.heartbeatOutgoing,\n            heartbeatStrategy: this.heartbeatStrategy,\n            splitLargeFrames: this.splitLargeFrames,\n            maxWebSocketChunkSize: this.maxWebSocketChunkSize,\n            forceBinaryWSFrames: this.forceBinaryWSFrames,\n            logRawCommunication: this.logRawCommunication,\n            appendMissingNULLonIncoming: this.appendMissingNULLonIncoming,\n            discardWebsocketOnCommFailure: this.discardWebsocketOnCommFailure,\n            onConnect: frame => {\n                // Successfully connected, stop the connection watcher\n                if (this._connectionWatcher) {\n                    clearTimeout(this._connectionWatcher);\n                    this._connectionWatcher = undefined;\n                }\n                if (!this.active) {\n                    this.debug('STOMP got connected while deactivate was issued, will disconnect now');\n                    this._disposeStompHandler();\n                    return;\n                }\n                this.onConnect(frame);\n            },\n            onDisconnect: frame => {\n                this.onDisconnect(frame);\n            },\n            onStompError: frame => {\n                this.onStompError(frame);\n            },\n            onWebSocketClose: evt => {\n                this._stompHandler = undefined; // a new one will be created in case of a reconnect\n                if (this.state === ActivationState.DEACTIVATING) {\n                    // Mark deactivation complete\n                    this._changeState(ActivationState.INACTIVE);\n                }\n                // The callback is called before attempting to reconnect, this would allow the client\n                // to be `deactivated` in the callback.\n                this.onWebSocketClose(evt);\n                if (this.active) {\n                    this._schedule_reconnect();\n                }\n            },\n            onWebSocketError: evt => {\n                this.onWebSocketError(evt);\n            },\n            onUnhandledMessage: message => {\n                this.onUnhandledMessage(message);\n            },\n            onUnhandledReceipt: frame => {\n                this.onUnhandledReceipt(frame);\n            },\n            onUnhandledFrame: frame => {\n                this.onUnhandledFrame(frame);\n            },\n        });\n        this._stompHandler.start();\n    }\n    _createWebSocket() {\n        let webSocket;\n        if (this.webSocketFactory) {\n            webSocket = this.webSocketFactory();\n        }\n        else if (this.brokerURL) {\n            webSocket = new WebSocket(this.brokerURL, this.stompVersions.protocolVersions());\n        }\n        else {\n            throw new Error('Either brokerURL or webSocketFactory must be provided');\n        }\n        webSocket.binaryType = 'arraybuffer';\n        return webSocket;\n    }\n    _schedule_reconnect() {\n        if (this._nextReconnectDelay > 0) {\n            this.debug(`STOMP: scheduling reconnection in ${this._nextReconnectDelay}ms`);\n            this._reconnector = setTimeout(() => {\n                if (this.reconnectTimeMode === ReconnectionTimeMode.EXPONENTIAL) {\n                    this._nextReconnectDelay = this._nextReconnectDelay * 2;\n                    // Truncated exponential backoff with a set limit unless disabled\n                    if (this.maxReconnectDelay !== 0) {\n                        this._nextReconnectDelay = Math.min(this._nextReconnectDelay, this.maxReconnectDelay);\n                    }\n                }\n                this._connect();\n            }, this._nextReconnectDelay);\n        }\n    }\n    /**\n     * Disconnect if connected and stop auto reconnect loop.\n     * Appropriate callbacks will be invoked if there is an underlying STOMP connection.\n     *\n     * This call is async. It will resolve immediately if there is no underlying active websocket,\n     * otherwise, it will resolve after the underlying websocket is properly disposed of.\n     *\n     * It is not an error to invoke this method more than once.\n     * Each of those would resolve on completion of deactivation.\n     *\n     * To reactivate, you can call [Client#activate]{@link Client#activate}.\n     *\n     * Experimental: pass `force: true` to immediately discard the underlying connection.\n     * This mode will skip both the STOMP and the Websocket shutdown sequences.\n     * In some cases, browsers take a long time in the Websocket shutdown\n     * if the underlying connection had gone stale.\n     * Using this mode can speed up.\n     * When this mode is used, the actual Websocket may linger for a while\n     * and the broker may not realize that the connection is no longer in use.\n     *\n     * It is possible to invoke this method initially without the `force` option\n     * and subsequently, say after a wait, with the `force` option.\n     */\n    async deactivate(options = {}) {\n        const force = options.force || false;\n        const needToDispose = this.active;\n        let retPromise;\n        if (this.state === ActivationState.INACTIVE) {\n            this.debug(`Already INACTIVE, nothing more to do`);\n            return Promise.resolve();\n        }\n        this._changeState(ActivationState.DEACTIVATING);\n        // Reset reconnection timer just to be safe\n        this._nextReconnectDelay = 0;\n        // Clear if a reconnection was scheduled\n        if (this._reconnector) {\n            clearTimeout(this._reconnector);\n            this._reconnector = undefined;\n        }\n        if (this._stompHandler &&\n            // @ts-ignore - if there is a _stompHandler, there is the webSocket\n            this.webSocket.readyState !== StompSocketState.CLOSED) {\n            const origOnWebSocketClose = this._stompHandler.onWebSocketClose;\n            // we need to wait for the underlying websocket to close\n            retPromise = new Promise((resolve, reject) => {\n                // @ts-ignore - there is a _stompHandler\n                this._stompHandler.onWebSocketClose = evt => {\n                    origOnWebSocketClose(evt);\n                    resolve();\n                };\n            });\n        }\n        else {\n            // indicate that auto reconnect loop should terminate\n            this._changeState(ActivationState.INACTIVE);\n            return Promise.resolve();\n        }\n        if (force) {\n            this._stompHandler?.discardWebsocket();\n        }\n        else if (needToDispose) {\n            this._disposeStompHandler();\n        }\n        return retPromise;\n    }\n    /**\n     * Force disconnect if there is an active connection by directly closing the underlying WebSocket.\n     * This is different from a normal disconnect where a DISCONNECT sequence is carried out with the broker.\n     * After forcing disconnect, automatic reconnect will be attempted.\n     * To stop further reconnects call [Client#deactivate]{@link Client#deactivate} as well.\n     */\n    forceDisconnect() {\n        if (this._stompHandler) {\n            this._stompHandler.forceDisconnect();\n        }\n    }\n    _disposeStompHandler() {\n        // Dispose STOMP Handler\n        if (this._stompHandler) {\n            this._stompHandler.dispose();\n        }\n    }\n    /**\n     * Send a message to a named destination. Refer to your STOMP broker documentation for types\n     * and naming of destinations.\n     *\n     * STOMP protocol specifies and suggests some headers and also allows broker-specific headers.\n     *\n     * `body` must be String.\n     * You will need to covert the payload to string in case it is not string (e.g. JSON).\n     *\n     * To send a binary message body, use `binaryBody` parameter. It should be a\n     * [Uint8Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint8Array).\n     * Sometimes brokers may not support binary frames out of the box.\n     * Please check your broker documentation.\n     *\n     * `content-length` header is automatically added to the STOMP Frame sent to the broker.\n     * Set `skipContentLengthHeader` to indicate that `content-length` header should not be added.\n     * For binary messages, `content-length` header is always added.\n     *\n     * Caution: The broker will, most likely, report an error and disconnect\n     * if the message body has NULL octet(s) and `content-length` header is missing.\n     *\n     * ```javascript\n     *        client.publish({destination: \"/queue/test\", headers: {priority: 9}, body: \"Hello, STOMP\"});\n     *\n     *        // Only destination is mandatory parameter\n     *        client.publish({destination: \"/queue/test\", body: \"Hello, STOMP\"});\n     *\n     *        // Skip content-length header in the frame to the broker\n     *        client.publish({\"/queue/test\", body: \"Hello, STOMP\", skipContentLengthHeader: true});\n     *\n     *        var binaryData = generateBinaryData(); // This need to be of type Uint8Array\n     *        // setting content-type header is not mandatory, however a good practice\n     *        client.publish({destination: '/topic/special', binaryBody: binaryData,\n     *                         headers: {'content-type': 'application/octet-stream'}});\n     * ```\n     */\n    publish(params) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        this._stompHandler.publish(params);\n    }\n    _checkConnection() {\n        if (!this.connected) {\n            throw new TypeError('There is no underlying STOMP connection');\n        }\n    }\n    /**\n     * STOMP brokers may carry out operation asynchronously and allow requesting for acknowledgement.\n     * To request an acknowledgement, a `receipt` header needs to be sent with the actual request.\n     * The value (say receipt-id) for this header needs to be unique for each use.\n     * Typically, a sequence, a UUID, a random number or a combination may be used.\n     *\n     * A complaint broker will send a RECEIPT frame when an operation has actually been completed.\n     * The operation needs to be matched based on the value of the receipt-id.\n     *\n     * This method allows watching for a receipt and invoking the callback\n     *  when the corresponding receipt has been received.\n     *\n     * The actual {@link IFrame} will be passed as parameter to the callback.\n     *\n     * Example:\n     * ```javascript\n     *        // Subscribing with acknowledgement\n     *        let receiptId = randomText();\n     *\n     *        client.watchForReceipt(receiptId, function() {\n     *          // Will be called after server acknowledges\n     *        });\n     *\n     *        client.subscribe(TEST.destination, onMessage, {receipt: receiptId});\n     *\n     *\n     *        // Publishing with acknowledgement\n     *        receiptId = randomText();\n     *\n     *        client.watchForReceipt(receiptId, function() {\n     *          // Will be called after server acknowledges\n     *        });\n     *        client.publish({destination: TEST.destination, headers: {receipt: receiptId}, body: msg});\n     * ```\n     */\n    watchForReceipt(receiptId, callback) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        this._stompHandler.watchForReceipt(receiptId, callback);\n    }\n    /**\n     * Subscribe to a STOMP Broker location. The callback will be invoked for each\n     * received message with the {@link IMessage} as argument.\n     *\n     * Note: The library will generate a unique ID if there is none provided in the headers.\n     *       To use your own ID, pass it using the `headers` argument.\n     *\n     * ```javascript\n     *        callback = function(message) {\n     *        // called when the client receives a STOMP message from the server\n     *          if (message.body) {\n     *            alert(\"got message with body \" + message.body)\n     *          } else {\n     *            alert(\"got empty message\");\n     *          }\n     *        });\n     *\n     *        var subscription = client.subscribe(\"/queue/test\", callback);\n     *\n     *        // Explicit subscription id\n     *        var mySubId = 'my-subscription-id-001';\n     *        var subscription = client.subscribe(destination, callback, { id: mySubId });\n     * ```\n     */\n    subscribe(destination, callback, headers = {}) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        return this._stompHandler.subscribe(destination, callback, headers);\n    }\n    /**\n     * It is preferable to unsubscribe from a subscription by calling\n     * `unsubscribe()` directly on {@link StompSubscription} returned by `client.subscribe()`:\n     *\n     * ```javascript\n     *        var subscription = client.subscribe(destination, onmessage);\n     *        // ...\n     *        subscription.unsubscribe();\n     * ```\n     *\n     * See: https://stomp.github.com/stomp-specification-1.2.html#UNSUBSCRIBE UNSUBSCRIBE Frame\n     */\n    unsubscribe(id, headers = {}) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        this._stompHandler.unsubscribe(id, headers);\n    }\n    /**\n     * Start a transaction, the returned {@link ITransaction} has methods - [commit]{@link ITransaction#commit}\n     * and [abort]{@link ITransaction#abort}.\n     *\n     * `transactionId` is optional, if not passed the library will generate it internally.\n     */\n    begin(transactionId) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        return this._stompHandler.begin(transactionId);\n    }\n    /**\n     * Commit a transaction.\n     *\n     * It is preferable to commit a transaction by calling [commit]{@link ITransaction#commit} directly on\n     * {@link ITransaction} returned by [client.begin]{@link Client#begin}.\n     *\n     * ```javascript\n     *        var tx = client.begin(txId);\n     *        //...\n     *        tx.commit();\n     * ```\n     */\n    commit(transactionId) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        this._stompHandler.commit(transactionId);\n    }\n    /**\n     * Abort a transaction.\n     * It is preferable to abort a transaction by calling [abort]{@link ITransaction#abort} directly on\n     * {@link ITransaction} returned by [client.begin]{@link Client#begin}.\n     *\n     * ```javascript\n     *        var tx = client.begin(txId);\n     *        //...\n     *        tx.abort();\n     * ```\n     */\n    abort(transactionId) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        this._stompHandler.abort(transactionId);\n    }\n    /**\n     * ACK a message. It is preferable to acknowledge a message by calling [ack]{@link IMessage#ack} directly\n     * on the {@link IMessage} handled by a subscription callback:\n     *\n     * ```javascript\n     *        var callback = function (message) {\n     *          // process the message\n     *          // acknowledge it\n     *          message.ack();\n     *        };\n     *        client.subscribe(destination, callback, {'ack': 'client'});\n     * ```\n     */\n    ack(messageId, subscriptionId, headers = {}) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        this._stompHandler.ack(messageId, subscriptionId, headers);\n    }\n    /**\n     * NACK a message. It is preferable to acknowledge a message by calling [nack]{@link IMessage#nack} directly\n     * on the {@link IMessage} handled by a subscription callback:\n     *\n     * ```javascript\n     *        var callback = function (message) {\n     *          // process the message\n     *          // an error occurs, nack it\n     *          message.nack();\n     *        };\n     *        client.subscribe(destination, callback, {'ack': 'client'});\n     * ```\n     */\n    nack(messageId, subscriptionId, headers = {}) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        this._stompHandler.nack(messageId, subscriptionId, headers);\n    }\n}\n", "/**\n * Configuration options for STOMP Client, each key corresponds to\n * field by the same name in {@link Client}. This can be passed to\n * the constructor of {@link Client} or to [Client#configure]{@link Client#configure}.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class StompConfig {\n}\n", "/**\n * STOMP headers. Many functions calls will accept headers as parameters.\n * The headers sent by <PERSON><PERSON><PERSON> will be available as [IFrame#headers]{@link IFrame#headers}.\n *\n * `key` and `value` must be valid strings.\n * In addition, `key` must not contain `CR`, `LF`, or `:`.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class StompHeaders {\n}\n", "/**\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class HeartbeatInfo {\n    constructor(client) {\n        this.client = client;\n    }\n    get outgoing() {\n        return this.client.heartbeatOutgoing;\n    }\n    set outgoing(value) {\n        this.client.heartbeatOutgoing = value;\n    }\n    get incoming() {\n        return this.client.heartbeatIncoming;\n    }\n    set incoming(value) {\n        this.client.heartbeatIncoming = value;\n    }\n}\n", "import { Client } from '../client.js';\nimport { HeartbeatInfo } from './heartbeat-info.js';\n/**\n * Available for backward compatibility, please shift to using {@link Client}.\n *\n * **Deprecated**\n *\n * Part of `@stomp/stompjs`.\n *\n * To upgrade, please follow the [Upgrade Guide](https://stomp-js.github.io/guide/stompjs/upgrading-stompjs.html)\n */\nexport class CompatClient extends Client {\n    /**\n     * Available for backward compatibility, please shift to using {@link Client}\n     * and [Client#webSocketFactory]{@link Client#webSocketFactory}.\n     *\n     * **Deprecated**\n     *\n     * @internal\n     */\n    constructor(webSocketFactory) {\n        super();\n        /**\n         * It is no op now. No longer needed. Large packets work out of the box.\n         */\n        this.maxWebSocketFrameSize = 16 * 1024;\n        this._heartbeatInfo = new HeartbeatInfo(this);\n        this.reconnect_delay = 0;\n        this.webSocketFactory = webSocketFactory;\n        // Default from previous version\n        this.debug = (...message) => {\n            console.log(...message);\n        };\n    }\n    _parseConnect(...args) {\n        let closeEventCallback;\n        let connectCallback;\n        let errorCallback;\n        let headers = {};\n        if (args.length < 2) {\n            throw new Error('Connect requires at least 2 arguments');\n        }\n        if (typeof args[1] === 'function') {\n            [headers, connectCallback, errorCallback, closeEventCallback] = args;\n        }\n        else {\n            switch (args.length) {\n                case 6:\n                    [\n                        headers.login,\n                        headers.passcode,\n                        connectCallback,\n                        errorCallback,\n                        closeEventCallback,\n                        headers.host,\n                    ] = args;\n                    break;\n                default:\n                    [\n                        headers.login,\n                        headers.passcode,\n                        connectCallback,\n                        errorCallback,\n                        closeEventCallback,\n                    ] = args;\n            }\n        }\n        return [headers, connectCallback, errorCallback, closeEventCallback];\n    }\n    /**\n     * Available for backward compatibility, please shift to using [Client#activate]{@link Client#activate}.\n     *\n     * **Deprecated**\n     *\n     * The `connect` method accepts different number of arguments and types. See the Overloads list. Use the\n     * version with headers to pass your broker specific options.\n     *\n     * overloads:\n     * - connect(headers, connectCallback)\n     * - connect(headers, connectCallback, errorCallback)\n     * - connect(login, passcode, connectCallback)\n     * - connect(login, passcode, connectCallback, errorCallback)\n     * - connect(login, passcode, connectCallback, errorCallback, closeEventCallback)\n     * - connect(login, passcode, connectCallback, errorCallback, closeEventCallback, host)\n     *\n     * params:\n     * - headers, see [Client#connectHeaders]{@link Client#connectHeaders}\n     * - connectCallback, see [Client#onConnect]{@link Client#onConnect}\n     * - errorCallback, see [Client#onStompError]{@link Client#onStompError}\n     * - closeEventCallback, see [Client#onWebSocketClose]{@link Client#onWebSocketClose}\n     * - login [String], see [Client#connectHeaders](../classes/Client.html#connectHeaders)\n     * - passcode [String], [Client#connectHeaders](../classes/Client.html#connectHeaders)\n     * - host [String], see [Client#connectHeaders](../classes/Client.html#connectHeaders)\n     *\n     * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n     */\n    connect(...args) {\n        const out = this._parseConnect(...args);\n        if (out[0]) {\n            this.connectHeaders = out[0];\n        }\n        if (out[1]) {\n            this.onConnect = out[1];\n        }\n        if (out[2]) {\n            this.onStompError = out[2];\n        }\n        if (out[3]) {\n            this.onWebSocketClose = out[3];\n        }\n        super.activate();\n    }\n    /**\n     * Available for backward compatibility, please shift to using [Client#deactivate]{@link Client#deactivate}.\n     *\n     * **Deprecated**\n     *\n     * See:\n     * [Client#onDisconnect]{@link Client#onDisconnect}, and\n     * [Client#disconnectHeaders]{@link Client#disconnectHeaders}\n     *\n     * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n     */\n    disconnect(disconnectCallback, headers = {}) {\n        if (disconnectCallback) {\n            this.onDisconnect = disconnectCallback;\n        }\n        this.disconnectHeaders = headers;\n        super.deactivate();\n    }\n    /**\n     * Available for backward compatibility, use [Client#publish]{@link Client#publish}.\n     *\n     * Send a message to a named destination. Refer to your STOMP broker documentation for types\n     * and naming of destinations. The headers will, typically, be available to the subscriber.\n     * However, there may be special purpose headers corresponding to your STOMP broker.\n     *\n     *  **Deprecated**, use [Client#publish]{@link Client#publish}\n     *\n     * Note: Body must be String. You will need to covert the payload to string in case it is not string (e.g. JSON)\n     *\n     * ```javascript\n     *        client.send(\"/queue/test\", {priority: 9}, \"Hello, STOMP\");\n     *\n     *        // If you want to send a message with a body, you must also pass the headers argument.\n     *        client.send(\"/queue/test\", {}, \"Hello, STOMP\");\n     * ```\n     *\n     * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n     */\n    send(destination, headers = {}, body = '') {\n        headers = Object.assign({}, headers);\n        const skipContentLengthHeader = headers['content-length'] === false;\n        if (skipContentLengthHeader) {\n            delete headers['content-length'];\n        }\n        this.publish({\n            destination,\n            headers: headers,\n            body,\n            skipContentLengthHeader,\n        });\n    }\n    /**\n     * Available for backward compatibility, renamed to [Client#reconnectDelay]{@link Client#reconnectDelay}.\n     *\n     * **Deprecated**\n     */\n    set reconnect_delay(value) {\n        this.reconnectDelay = value;\n    }\n    /**\n     * Available for backward compatibility, renamed to [Client#webSocket]{@link Client#webSocket}.\n     *\n     * **Deprecated**\n     */\n    get ws() {\n        return this.webSocket;\n    }\n    /**\n     * Available for backward compatibility, renamed to [Client#connectedVersion]{@link Client#connectedVersion}.\n     *\n     * **Deprecated**\n     */\n    get version() {\n        return this.connectedVersion;\n    }\n    /**\n     * Available for backward compatibility, renamed to [Client#onUnhandledMessage]{@link Client#onUnhandledMessage}.\n     *\n     * **Deprecated**\n     */\n    get onreceive() {\n        return this.onUnhandledMessage;\n    }\n    /**\n     * Available for backward compatibility, renamed to [Client#onUnhandledMessage]{@link Client#onUnhandledMessage}.\n     *\n     * **Deprecated**\n     */\n    set onreceive(value) {\n        this.onUnhandledMessage = value;\n    }\n    /**\n     * Available for backward compatibility, renamed to [Client#onUnhandledReceipt]{@link Client#onUnhandledReceipt}.\n     * Prefer using [Client#watchForReceipt]{@link Client#watchForReceipt}.\n     *\n     * **Deprecated**\n     */\n    get onreceipt() {\n        return this.onUnhandledReceipt;\n    }\n    /**\n     * Available for backward compatibility, renamed to [Client#onUnhandledReceipt]{@link Client#onUnhandledReceipt}.\n     *\n     * **Deprecated**\n     */\n    set onreceipt(value) {\n        this.onUnhandledReceipt = value;\n    }\n    /**\n     * Available for backward compatibility, renamed to [Client#heartbeatIncoming]{@link Client#heartbeatIncoming}\n     * [Client#heartbeatOutgoing]{@link Client#heartbeatOutgoing}.\n     *\n     * **Deprecated**\n     */\n    get heartbeat() {\n        return this._heartbeatInfo;\n    }\n    /**\n     * Available for backward compatibility, renamed to [Client#heartbeatIncoming]{@link Client#heartbeatIncoming}\n     * [Client#heartbeatOutgoing]{@link Client#heartbeatOutgoing}.\n     *\n     * **Deprecated**\n     */\n    set heartbeat(value) {\n        this.heartbeatIncoming = value.incoming;\n        this.heartbeatOutgoing = value.outgoing;\n    }\n}\n", "import { Versions } from '../versions.js';\nimport { CompatClient } from './compat-client.js';\n/**\n * STOMP Class, acts like a factory to create {@link Client}.\n *\n * Part of `@stomp/stompjs`.\n *\n * **Deprecated**\n *\n * It will be removed in next major version. Please switch to {@link Client}.\n */\nexport class Stomp {\n    /**\n     * This method creates a WebSocket client that is connected to\n     * the STOMP server located at the url.\n     *\n     * ```javascript\n     *        var url = \"ws://localhost:61614/stomp\";\n     *        var client = Stomp.client(url);\n     * ```\n     *\n     * **Deprecated**\n     *\n     * It will be removed in next major version. Please switch to {@link Client}\n     * using [Client#brokerURL]{@link Client#brokerURL}.\n     */\n    static client(url, protocols) {\n        // This is a hack to allow another implementation than the standard\n        // HTML5 WebSocket class.\n        //\n        // It is possible to use another class by calling\n        //\n        //     Stomp.WebSocketClass = MozWebSocket\n        //\n        // *prior* to call `Stomp.client()`.\n        //\n        // This hack is deprecated and `Stomp.over()` method should be used\n        // instead.\n        // See remarks on the function Stomp.over\n        if (protocols == null) {\n            protocols = Versions.default.protocolVersions();\n        }\n        const wsFn = () => {\n            const klass = Stomp.WebSocketClass || WebSocket;\n            return new klass(url, protocols);\n        };\n        return new CompatClient(wsFn);\n    }\n    /**\n     * This method is an alternative to [Stomp#client]{@link Stomp#client} to let the user\n     * specify the WebSocket to use (either a standard HTML5 WebSocket or\n     * a similar object).\n     *\n     * In order to support reconnection, the function Client._connect should be callable more than once.\n     * While reconnecting\n     * a new instance of underlying transport (TCP Socket, WebSocket or SockJS) will be needed. So, this function\n     * alternatively allows passing a function that should return a new instance of the underlying socket.\n     *\n     * ```javascript\n     *        var client = Stomp.over(function(){\n     *          return new WebSocket('ws://localhost:15674/ws')\n     *        });\n     * ```\n     *\n     * **Deprecated**\n     *\n     * It will be removed in next major version. Please switch to {@link Client}\n     * using [Client#webSocketFactory]{@link Client#webSocketFactory}.\n     */\n    static over(ws) {\n        let wsFn;\n        if (typeof ws === 'function') {\n            wsFn = ws;\n        }\n        else {\n            console.warn('Stomp.over did not receive a factory, auto reconnect will not work. ' +\n                'Please see https://stomp-js.github.io/api-docs/latest/classes/Stomp.html#over');\n            wsFn = () => ws;\n        }\n        return new CompatClient(wsFn);\n    }\n}\n/**\n * In case you need to use a non standard class for WebSocket.\n *\n * For example when using within NodeJS environment:\n *\n * ```javascript\n *        StompJs = require('../../esm5/');\n *        Stomp = StompJs.Stomp;\n *        Stomp.WebSocketClass = require('websocket').w3cwebsocket;\n * ```\n *\n * **Deprecated**\n *\n *\n * It will be removed in next major version. Please switch to {@link Client}\n * using [Client#webSocketFactory]{@link Client#webSocketFactory}.\n */\n// tslint:disable-next-line:variable-name\nStomp.WebSocketClass = null;\n"], "mappings": ";;;;;AAGO,SAAS,iBAAiB,WAAW,OAAO;AAC/C,YAAU,YAAY,WAAY;AAC9B,UAAM,OAAO,MAAM;AAAA,IAAE;AAErB,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,UAAM,KAAK,oBAAI,KAAK;AACpB,UAAM,KAAK,KAAK,OAAO,EAAE,SAAS,EAAE,UAAU,GAAG,CAAC;AAClD,UAAM,cAAc,KAAK;AAEzB,SAAK,UAAU,gBAAc;AACzB,YAAM,SAAQ,oBAAI,KAAK,GAAE,QAAQ,IAAI,GAAG,QAAQ;AAChD,YAAM,sBAAsB,EAAE,mBAAmB,KAAK,yBAAyB,WAAW,IAAI,IAAI,WAAW,MAAM,EAAE;AAAA,IACzH;AACA,SAAK,MAAM;AACX,iBAAa,KAAK,WAAW;AAAA,MACzB,MAAM;AAAA,MACN,QAAQ,6BAA6B,EAAE;AAAA,MACvC,UAAU;AAAA,IACd,CAAC;AAAA,EACL;AACJ;;;AClBO,IAAM,OAAO;AAAA;AAAA,EAEhB,IAAI;AAAA;AAAA,EAEJ,MAAM;AACV;;;ACNO,IAAM,YAAN,MAAM,WAAU;AAAA;AAAA;AAAA;AAAA,EAInB,IAAI,OAAO;AACP,QAAI,CAAC,KAAK,SAAS,KAAK,cAAc;AAClC,WAAK,QAAQ,IAAI,YAAY,EAAE,OAAO,KAAK,WAAW;AAAA,IAC1D;AACA,WAAO,KAAK,SAAS;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,aAAa;AACb,QAAI,CAAC,KAAK,eAAe,CAAC,KAAK,cAAc;AACzC,WAAK,cAAc,IAAI,YAAY,EAAE,OAAO,KAAK,KAAK;AAAA,IAC1D;AAEA,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,QAAQ;AAChB,UAAM,EAAE,SAAS,SAAS,MAAM,YAAY,oBAAoB,wBAAyB,IAAI;AAC7F,SAAK,UAAU;AACf,SAAK,UAAU,OAAO,OAAO,CAAC,GAAG,WAAW,CAAC,CAAC;AAC9C,QAAI,YAAY;AACZ,WAAK,cAAc;AACnB,WAAK,eAAe;AAAA,IACxB,OACK;AACD,WAAK,QAAQ,QAAQ;AACrB,WAAK,eAAe;AAAA,IACxB;AACA,SAAK,qBAAqB,sBAAsB;AAChD,SAAK,0BAA0B,2BAA2B;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,aAAa,UAAU,oBAAoB;AAC9C,UAAM,UAAU,CAAC;AACjB,UAAM,OAAO,CAAC,QAAQ,IAAI,QAAQ,cAAc,EAAE;AAElD,eAAW,UAAU,SAAS,QAAQ,QAAQ,GAAG;AAC7C,YAAM,MAAM,OAAO,QAAQ,GAAG;AAC9B,YAAM,MAAM,KAAK,OAAO,CAAC,CAAC;AAC1B,UAAI,QAAQ,KAAK,OAAO,CAAC,CAAC;AAC1B,UAAI,sBACA,SAAS,YAAY,aACrB,SAAS,YAAY,aAAa;AAClC,gBAAQ,WAAU,iBAAiB,KAAK;AAAA,MAC5C;AACA,cAAQ,GAAG,IAAI;AAAA,IACnB;AACA,WAAO,IAAI,WAAU;AAAA,MACjB,SAAS,SAAS;AAAA,MAClB;AAAA,MACA,YAAY,SAAS;AAAA,MACrB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,WAAO,KAAK,uBAAuB;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AACR,UAAM,gBAAgB,KAAK,uBAAuB;AAClD,QAAI,KAAK,cAAc;AACnB,aAAO,WAAU,aAAa,eAAe,KAAK,WAAW,EAAE;AAAA,IACnE,OACK;AACD,aAAO,gBAAgB,KAAK,QAAQ,KAAK;AAAA,IAC7C;AAAA,EACJ;AAAA,EACA,yBAAyB;AACrB,UAAM,QAAQ,CAAC,KAAK,OAAO;AAC3B,QAAI,KAAK,yBAAyB;AAC9B,aAAO,KAAK,QAAQ,gBAAgB;AAAA,IACxC;AACA,eAAW,QAAQ,OAAO,KAAK,KAAK,WAAW,CAAC,CAAC,GAAG;AAChD,YAAM,QAAQ,KAAK,QAAQ,IAAI;AAC/B,UAAI,KAAK,sBACL,KAAK,YAAY,aACjB,KAAK,YAAY,aAAa;AAC9B,cAAM,KAAK,GAAG,IAAI,IAAI,WAAU,eAAe,GAAG,KAAK,EAAE,CAAC,EAAE;AAAA,MAChE,OACK;AACD,cAAM,KAAK,GAAG,IAAI,IAAI,KAAK,EAAE;AAAA,MACjC;AAAA,IACJ;AACA,QAAI,KAAK,gBACJ,CAAC,KAAK,YAAY,KAAK,CAAC,KAAK,yBAA0B;AACxD,YAAM,KAAK,kBAAkB,KAAK,WAAW,CAAC,EAAE;AAAA,IACpD;AACA,WAAO,MAAM,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK;AAAA,EAChD;AAAA,EACA,cAAc;AACV,WAAO,KAAK,WAAW,MAAM;AAAA,EACjC;AAAA,EACA,aAAa;AACT,UAAM,aAAa,KAAK;AACxB,WAAO,aAAa,WAAW,SAAS;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,WAAW,GAAG;AACjB,WAAO,IAAI,IAAI,YAAY,EAAE,OAAO,CAAC,EAAE,SAAS;AAAA,EACpD;AAAA,EACA,OAAO,aAAa,eAAe,YAAY;AAC3C,UAAM,qBAAqB,IAAI,YAAY,EAAE,OAAO,aAAa;AACjE,UAAM,iBAAiB,IAAI,WAAW,CAAC,CAAC,CAAC;AACzC,UAAM,aAAa,IAAI,WAAW,mBAAmB,SAAS,WAAW,SAAS,eAAe,MAAM;AACvG,eAAW,IAAI,kBAAkB;AACjC,eAAW,IAAI,YAAY,mBAAmB,MAAM;AACpD,eAAW,IAAI,gBAAgB,mBAAmB,SAAS,WAAW,MAAM;AAC5E,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,SAAS,QAAQ;AACpB,UAAM,QAAQ,IAAI,WAAU,MAAM;AAClC,WAAO,MAAM,UAAU;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,eAAe,KAAK;AACvB,WAAO,IACF,QAAQ,OAAO,MAAM,EACrB,QAAQ,OAAO,KAAK,EACpB,QAAQ,OAAO,KAAK,EACpB,QAAQ,MAAM,KAAK;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,iBAAiB,KAAK;AACzB,WAAO,IACF,QAAQ,QAAQ,IAAI,EACpB,QAAQ,QAAQ,IAAI,EACpB,QAAQ,QAAQ,GAAG,EACnB,QAAQ,SAAS,IAAI;AAAA,EAC9B;AACJ;;;ACtKA,IAAM,OAAO;AAIb,IAAM,KAAK;AAIX,IAAM,KAAK;AAIX,IAAM,QAAQ;AA2CP,IAAM,SAAN,MAAa;AAAA,EAChB,YAAY,SAAS,gBAAgB;AACjC,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,SAAK,WAAW,IAAI,YAAY;AAChC,SAAK,WAAW,IAAI,YAAY;AAChC,SAAK,SAAS,CAAC;AACf,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,WAAW,SAAS,8BAA8B,OAAO;AACrD,QAAI;AACJ,QAAI,OAAO,YAAY,UAAU;AAC7B,cAAQ,KAAK,SAAS,OAAO,OAAO;AAAA,IACxC,OACK;AACD,cAAQ,IAAI,WAAW,OAAO;AAAA,IAClC;AAKA,QAAI,+BAA+B,MAAM,MAAM,SAAS,CAAC,MAAM,GAAG;AAC9D,YAAM,gBAAgB,IAAI,WAAW,MAAM,SAAS,CAAC;AACrD,oBAAc,IAAI,OAAO,CAAC;AAC1B,oBAAc,MAAM,MAAM,IAAI;AAC9B,cAAQ;AAAA,IACZ;AAEA,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,YAAM,OAAO,MAAM,CAAC;AACpB,WAAK,QAAQ,IAAI;AAAA,IACrB;AAAA,EACJ;AAAA;AAAA;AAAA,EAGA,cAAc,MAAM;AAChB,QAAI,SAAS,MAAM;AAEf;AAAA,IACJ;AACA,QAAI,SAAS,IAAI;AAEb;AAAA,IACJ;AACA,QAAI,SAAS,IAAI;AAEb,WAAK,eAAe;AACpB;AAAA,IACJ;AACA,SAAK,UAAU,KAAK;AACpB,SAAK,cAAc,IAAI;AAAA,EAC3B;AAAA,EACA,gBAAgB,MAAM;AAClB,QAAI,SAAS,IAAI;AAEb;AAAA,IACJ;AACA,QAAI,SAAS,IAAI;AACb,WAAK,SAAS,UAAU,KAAK,oBAAoB;AACjD,WAAK,UAAU,KAAK;AACpB;AAAA,IACJ;AACA,SAAK,aAAa,IAAI;AAAA,EAC1B;AAAA,EACA,gBAAgB,MAAM;AAClB,QAAI,SAAS,IAAI;AAEb;AAAA,IACJ;AACA,QAAI,SAAS,IAAI;AACb,WAAK,kBAAkB;AACvB;AAAA,IACJ;AACA,SAAK,UAAU,KAAK;AACpB,SAAK,cAAc,IAAI;AAAA,EAC3B;AAAA,EACA,cAAc,MAAM;AAChB,SAAK,QAAQ,IAAI;AAAA,EACrB;AAAA,EACA,kBAAkB,MAAM;AACpB,QAAI,SAAS,OAAO;AAChB,WAAK,aAAa,KAAK,oBAAoB;AAC3C,WAAK,UAAU,KAAK;AACpB;AAAA,IACJ;AACA,SAAK,aAAa,IAAI;AAAA,EAC1B;AAAA,EACA,oBAAoB,MAAM;AACtB,QAAI,SAAS,IAAI;AAEb;AAAA,IACJ;AACA,QAAI,SAAS,IAAI;AACb,WAAK,SAAS,QAAQ,KAAK;AAAA,QACvB,KAAK;AAAA,QACL,KAAK,oBAAoB;AAAA,MAC7B,CAAC;AACD,WAAK,aAAa;AAClB,WAAK,UAAU,KAAK;AACpB;AAAA,IACJ;AACA,SAAK,aAAa,IAAI;AAAA,EAC1B;AAAA,EACA,oBAAoB;AAChB,UAAM,sBAAsB,KAAK,SAAS,QAAQ,OAAO,CAAC,WAAW;AACjE,aAAO,OAAO,CAAC,MAAM;AAAA,IACzB,CAAC,EAAE,CAAC;AACJ,QAAI,qBAAqB;AACrB,WAAK,sBAAsB,SAAS,oBAAoB,CAAC,GAAG,EAAE;AAC9D,WAAK,UAAU,KAAK;AAAA,IACxB,OACK;AACD,WAAK,UAAU,KAAK;AAAA,IACxB;AAAA,EACJ;AAAA,EACA,2BAA2B,MAAM;AAC7B,QAAI,SAAS,MAAM;AACf,WAAK,eAAe;AACpB;AAAA,IACJ;AACA,SAAK,aAAa,IAAI;AAAA,EAC1B;AAAA,EACA,sBAAsB,MAAM;AAExB,QAAI,KAAK,0BAA0B,GAAG;AAClC,WAAK,eAAe;AACpB;AAAA,IACJ;AACA,SAAK,aAAa,IAAI;AAAA,EAC1B;AAAA,EACA,iBAAiB;AACb,SAAK,SAAS,aAAa,KAAK,mBAAmB;AACnD,QAAI;AACA,WAAK,QAAQ,KAAK,QAAQ;AAAA,IAC9B,SACO,GAAG;AACN,cAAQ,IAAI,yEAAyE,CAAC;AAAA,IAC1F;AACA,SAAK,WAAW;AAAA,EACpB;AAAA;AAAA,EAEA,aAAa,MAAM;AACf,SAAK,OAAO,KAAK,IAAI;AAAA,EACzB;AAAA,EACA,sBAAsB;AAClB,WAAO,KAAK,SAAS,OAAO,KAAK,mBAAmB,CAAC;AAAA,EACzD;AAAA,EACA,qBAAqB;AACjB,UAAM,YAAY,IAAI,WAAW,KAAK,MAAM;AAC5C,SAAK,SAAS,CAAC;AACf,WAAO;AAAA,EACX;AAAA,EACA,aAAa;AACT,SAAK,WAAW;AAAA,MACZ,SAAS;AAAA,MACT,SAAS,CAAC;AAAA,MACV,YAAY;AAAA,IAChB;AACA,SAAK,SAAS,CAAC;AACf,SAAK,aAAa;AAClB,SAAK,UAAU,KAAK;AAAA,EACxB;AACJ;;;ACzNO,IAAI;AAAA,CACV,SAAUA,mBAAkB;AACzB,EAAAA,kBAAiBA,kBAAiB,YAAY,IAAI,CAAC,IAAI;AACvD,EAAAA,kBAAiBA,kBAAiB,MAAM,IAAI,CAAC,IAAI;AACjD,EAAAA,kBAAiBA,kBAAiB,SAAS,IAAI,CAAC,IAAI;AACpD,EAAAA,kBAAiBA,kBAAiB,QAAQ,IAAI,CAAC,IAAI;AACvD,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAIvC,IAAI;AAAA,CACV,SAAUC,kBAAiB;AACxB,EAAAA,iBAAgBA,iBAAgB,QAAQ,IAAI,CAAC,IAAI;AACjD,EAAAA,iBAAgBA,iBAAgB,cAAc,IAAI,CAAC,IAAI;AACvD,EAAAA,iBAAgBA,iBAAgB,UAAU,IAAI,CAAC,IAAI;AACvD,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAIrC,IAAI;AAAA,CACV,SAAUC,uBAAsB;AAC7B,EAAAA,sBAAqBA,sBAAqB,QAAQ,IAAI,CAAC,IAAI;AAC3D,EAAAA,sBAAqBA,sBAAqB,aAAa,IAAI,CAAC,IAAI;AACpE,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAI/C,IAAI;AAAA,CACV,SAAUC,iBAAgB;AACvB,EAAAA,gBAAe,UAAU,IAAI;AAC7B,EAAAA,gBAAe,QAAQ,IAAI;AAC/B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;;;ACjCnC,IAAM,SAAN,MAAa;AAAA,EAChB,YAAY,WAAW,YAAY,eAAe,UAAU,QAAQ;AAChE,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,gBAAgB;AAAA;AAAA;AAAA;AAAA,SAIpB,KAAK,SAAS;AAAA;AAAA,EAEnB;AAAA,EACA,MAAM,MAAM;AACR,SAAK,KAAK;AACV,QAAI,KAAK,gBAAgB,GAAG;AACxB,WAAK,UAAU,IAAI;AAAA,IACvB,OACK;AACD,WAAK,YAAY,IAAI;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,OAAO;AACH,SAAK,cAAc;AACnB,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,kBAAkB;AACd,WAAO,OAAQ,WAAY,eAAe,KAAK,cAAc,eAAe;AAAA,EAChF;AAAA,EACA,UAAU,MAAM;AACZ,SAAK,OAAO,oCAAoC;AAChD,QAAI,CAAC,KAAK,SAAS;AACf,WAAK,UAAU,IAAI,OAAO,IAAI,gBAAgB,IAAI,KAAK,CAAC,KAAK,aAAa,GAAG,EAAE,MAAM,kBAAkB,CAAC,CAAC,CAAC;AAC1G,WAAK,QAAQ,YAAY,CAAC,YAAY,KAAK,QAAQ,IAAI;AAAA,IAC3D;AAAA,EACJ;AAAA,EACA,YAAY,MAAM;AACd,SAAK,OAAO,sCAAsC;AAClD,QAAI,CAAC,KAAK,QAAQ;AACd,YAAM,YAAY,KAAK,IAAI;AAC3B,WAAK,SAAS,YAAY,MAAM;AAC5B,aAAK,KAAK,IAAI,IAAI,SAAS;AAAA,MAC/B,GAAG,KAAK,SAAS;AAAA,IACrB;AAAA,EACJ;AAAA,EACA,gBAAgB;AACZ,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,UAAU;AACvB,aAAO,KAAK;AACZ,WAAK,OAAO,6BAA6B;AAAA,IAC7C;AAAA,EACJ;AAAA,EACA,kBAAkB;AACd,QAAI,KAAK,QAAQ;AACb,oBAAc,KAAK,MAAM;AACzB,aAAO,KAAK;AACZ,WAAK,OAAO,+BAA+B;AAAA,IAC/C;AAAA,EACJ;AACJ;;;ACtDO,IAAM,WAAN,MAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOlB,YAAY,UAAU;AAClB,SAAK,WAAW;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAChB,WAAO,KAAK,SAAS,KAAK,GAAG;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB;AACf,WAAO,KAAK,SAAS,IAAI,OAAK,IAAI,EAAE,QAAQ,KAAK,EAAE,CAAC,QAAQ;AAAA,EAChE;AACJ;AAIA,SAAS,OAAO;AAIhB,SAAS,OAAO;AAIhB,SAAS,OAAO;AAIhB,SAAS,UAAU,IAAI,SAAS;AAAA,EAC5B,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb,CAAC;;;ACjCM,IAAM,eAAN,MAAmB;AAAA,EACtB,IAAI,mBAAmB;AACnB,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,YAAY,SAAS,YAAY,QAAQ;AACrC,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,uBAAuB;AAAA;AAAA,MAExB,WAAW,WAAS;AAChB,aAAK,MAAM,uBAAuB,MAAM,QAAQ,MAAM,EAAE;AACxD,aAAK,aAAa;AAClB,aAAK,oBAAoB,MAAM,QAAQ;AAEvC,YAAI,KAAK,sBAAsB,SAAS,MAAM;AAC1C,eAAK,sBAAsB;AAAA,QAC/B;AACA,aAAK,gBAAgB,MAAM,OAAO;AAClC,aAAK,UAAU,KAAK;AAAA,MACxB;AAAA;AAAA,MAEA,SAAS,WAAS;AAQd,cAAM,eAAe,MAAM,QAAQ;AACnC,cAAM,YAAY,KAAK,eAAe,YAAY,KAAK,KAAK;AAE5D,cAAM,UAAU;AAChB,cAAM,SAAS;AACf,cAAM,YAAY,KAAK,sBAAsB,SAAS,OAChD,QAAQ,QAAQ,MAChB,QAAQ,QAAQ,YAAY;AAGlC,gBAAQ,MAAM,CAAC,UAAU,CAAC,MAAM;AAC5B,iBAAO,OAAO,IAAI,WAAW,cAAc,OAAO;AAAA,QACtD;AACA,gBAAQ,OAAO,CAAC,UAAU,CAAC,MAAM;AAC7B,iBAAO,OAAO,KAAK,WAAW,cAAc,OAAO;AAAA,QACvD;AACA,kBAAU,OAAO;AAAA,MACrB;AAAA;AAAA,MAEA,SAAS,WAAS;AACd,cAAM,WAAW,KAAK,iBAAiB,MAAM,QAAQ,YAAY,CAAC;AAClE,YAAI,UAAU;AACV,mBAAS,KAAK;AAEd,iBAAO,KAAK,iBAAiB,MAAM,QAAQ,YAAY,CAAC;AAAA,QAC5D,OACK;AACD,eAAK,mBAAmB,KAAK;AAAA,QACjC;AAAA,MACJ;AAAA;AAAA,MAEA,OAAO,WAAS;AACZ,aAAK,aAAa,KAAK;AAAA,MAC3B;AAAA,IACJ;AAEA,SAAK,WAAW;AAEhB,SAAK,iBAAiB,CAAC;AAEvB,SAAK,mBAAmB,CAAC;AACzB,SAAK,eAAe;AACpB,SAAK,sBAAsB;AAC3B,SAAK,wBAAwB,KAAK,IAAI;AACtC,SAAK,QAAQ,OAAO;AACpB,SAAK,gBAAgB,OAAO;AAC5B,SAAK,iBAAiB,OAAO;AAC7B,SAAK,oBAAoB,OAAO;AAChC,SAAK,oBAAoB,OAAO;AAChC,SAAK,oBAAoB,OAAO;AAChC,SAAK,mBAAmB,OAAO;AAC/B,SAAK,wBAAwB,OAAO;AACpC,SAAK,sBAAsB,OAAO;AAClC,SAAK,sBAAsB,OAAO;AAClC,SAAK,8BAA8B,OAAO;AAC1C,SAAK,gCAAgC,OAAO;AAC5C,SAAK,YAAY,OAAO;AACxB,SAAK,eAAe,OAAO;AAC3B,SAAK,eAAe,OAAO;AAC3B,SAAK,mBAAmB,OAAO;AAC/B,SAAK,mBAAmB,OAAO;AAC/B,SAAK,qBAAqB,OAAO;AACjC,SAAK,qBAAqB,OAAO;AACjC,SAAK,mBAAmB,OAAO;AAAA,EACnC;AAAA,EACA,QAAQ;AACJ,UAAM,SAAS,IAAI;AAAA;AAAA,MAEnB,cAAY;AACR,cAAM,QAAQ,UAAU,aAAa,UAAU,KAAK,mBAAmB;AAEvE,YAAI,CAAC,KAAK,qBAAqB;AAC3B,eAAK,MAAM,OAAO,KAAK,EAAE;AAAA,QAC7B;AACA,cAAM,qBAAqB,KAAK,qBAAqB,MAAM,OAAO,KAAK,KAAK;AAC5E,2BAAmB,KAAK;AAAA,MAC5B;AAAA;AAAA,MAEA,MAAM;AACF,aAAK,MAAM,UAAU;AAAA,MACzB;AAAA,IAAC;AACD,SAAK,WAAW,YAAY,CAAC,QAAQ;AACjC,WAAK,MAAM,eAAe;AAC1B,WAAK,wBAAwB,KAAK,IAAI;AACtC,UAAI,KAAK,qBAAqB;AAC1B,cAAM,mBAAmB,IAAI,gBAAgB,cACvC,IAAI,YAAY,EAAE,OAAO,IAAI,IAAI,IACjC,IAAI;AACV,aAAK,MAAM,OAAO,gBAAgB,EAAE;AAAA,MACxC;AACA,aAAO,WAAW,IAAI,MAAM,KAAK,2BAA2B;AAAA,IAChE;AACA,SAAK,WAAW,UAAU,CAAC,eAAe;AACtC,WAAK,MAAM,wBAAwB,KAAK,WAAW,GAAG,EAAE;AACxD,WAAK,SAAS;AACd,WAAK,iBAAiB,UAAU;AAAA,IACpC;AACA,SAAK,WAAW,UAAU,CAAC,eAAe;AACtC,WAAK,iBAAiB,UAAU;AAAA,IACpC;AACA,SAAK,WAAW,SAAS,MAAM;AAE3B,YAAM,iBAAiB,OAAO,OAAO,CAAC,GAAG,KAAK,cAAc;AAC5D,WAAK,MAAM,sBAAsB;AACjC,qBAAe,gBAAgB,IAAI,KAAK,cAAc,kBAAkB;AACxE,qBAAe,YAAY,IAAI;AAAA,QAC3B,KAAK;AAAA,QACL,KAAK;AAAA,MACT,EAAE,KAAK,GAAG;AACV,WAAK,UAAU,EAAE,SAAS,WAAW,SAAS,eAAe,CAAC;AAAA,IAClE;AAAA,EACJ;AAAA,EACA,gBAAgB,SAAS;AACrB,QAAI,QAAQ,YAAY,SAAS,QAC7B,QAAQ,YAAY,SAAS,MAAM;AACnC;AAAA,IACJ;AAGA,QAAI,CAAC,QAAQ,YAAY,GAAG;AACxB;AAAA,IACJ;AAIA,UAAM,CAAC,gBAAgB,cAAc,IAAI,QAAQ,YAAY,EACxD,MAAM,GAAG,EACT,IAAI,CAAC,MAAM,SAAS,GAAG,EAAE,CAAC;AAC/B,QAAI,KAAK,sBAAsB,KAAK,mBAAmB,GAAG;AACtD,YAAM,MAAM,KAAK,IAAI,KAAK,mBAAmB,cAAc;AAC3D,WAAK,MAAM,mBAAmB,GAAG,IAAI;AACrC,WAAK,UAAU,IAAI,OAAO,KAAK,KAAK,QAAQ,mBAAmB,KAAK,KAAK;AACzE,WAAK,QAAQ,MAAM,MAAM;AACrB,YAAI,KAAK,WAAW,eAAe,iBAAiB,MAAM;AACtD,eAAK,WAAW,KAAK,KAAK,EAAE;AAC5B,eAAK,MAAM,UAAU;AAAA,QACzB;AAAA,MACJ,CAAC;AAAA,IACL;AACA,QAAI,KAAK,sBAAsB,KAAK,mBAAmB,GAAG;AACtD,YAAM,MAAM,KAAK,IAAI,KAAK,mBAAmB,cAAc;AAC3D,WAAK,MAAM,oBAAoB,GAAG,IAAI;AACtC,WAAK,UAAU,YAAY,MAAM;AAC7B,cAAM,QAAQ,KAAK,IAAI,IAAI,KAAK;AAEhC,YAAI,QAAQ,MAAM,GAAG;AACjB,eAAK,MAAM,gDAAgD,KAAK,IAAI;AACpE,eAAK,yBAAyB;AAAA,QAClC;AAAA,MACJ,GAAG,GAAG;AAAA,IACV;AAAA,EACJ;AAAA,EACA,2BAA2B;AACvB,QAAI,KAAK,+BAA+B;AACpC,WAAK,MAAM,oEAAoE;AAC/E,WAAK,iBAAiB;AAAA,IAC1B,OACK;AACD,WAAK,MAAM,gCAAgC;AAC3C,WAAK,gBAAgB;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,kBAAkB;AACd,QAAI,KAAK,YAAY;AACjB,UAAI,KAAK,WAAW,eAAe,iBAAiB,cAChD,KAAK,WAAW,eAAe,iBAAiB,MAAM;AACtD,aAAK,yBAAyB;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,kBAAkB;AACd,SAAK,WAAW,YAAY,MAAM;AAAA,IAAE;AACpC,SAAK,WAAW,MAAM;AAAA,EAC1B;AAAA,EACA,mBAAmB;AACf,QAAI,OAAO,KAAK,WAAW,cAAc,YAAY;AACjD,uBAAiB,KAAK,YAAY,CAAC,QAAQ,KAAK,MAAM,GAAG,CAAC;AAAA,IAC9D;AAEA,SAAK,WAAW,UAAU;AAAA,EAC9B;AAAA,EACA,UAAU,QAAQ;AACd,UAAM,EAAE,SAAS,SAAS,MAAM,YAAY,wBAAwB,IAAI;AACxE,UAAM,QAAQ,IAAI,UAAU;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,oBAAoB,KAAK;AAAA,MACzB;AAAA,IACJ,CAAC;AACD,QAAI,WAAW,MAAM,UAAU;AAC/B,QAAI,KAAK,qBAAqB;AAC1B,WAAK,MAAM,OAAO,QAAQ,EAAE;AAAA,IAChC,OACK;AACD,WAAK,MAAM,OAAO,KAAK,EAAE;AAAA,IAC7B;AACA,QAAI,KAAK,uBAAuB,OAAO,aAAa,UAAU;AAC1D,iBAAW,IAAI,YAAY,EAAE,OAAO,QAAQ;AAAA,IAChD;AACA,QAAI,OAAO,aAAa,YAAY,CAAC,KAAK,kBAAkB;AACxD,WAAK,WAAW,KAAK,QAAQ;AAAA,IACjC,OACK;AACD,UAAI,MAAM;AACV,aAAO,IAAI,SAAS,GAAG;AACnB,cAAM,QAAQ,IAAI,UAAU,GAAG,KAAK,qBAAqB;AACzD,cAAM,IAAI,UAAU,KAAK,qBAAqB;AAC9C,aAAK,WAAW,KAAK,KAAK;AAC1B,aAAK,MAAM,gBAAgB,MAAM,MAAM,iBAAiB,IAAI,MAAM,EAAE;AAAA,MACxE;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,UAAU;AACN,QAAI,KAAK,WAAW;AAChB,UAAI;AAEA,cAAM,oBAAoB,OAAO,OAAO,CAAC,GAAG,KAAK,iBAAiB;AAClE,YAAI,CAAC,kBAAkB,SAAS;AAC5B,4BAAkB,UAAU,SAAS,KAAK,UAAU;AAAA,QACxD;AACA,aAAK,gBAAgB,kBAAkB,SAAS,WAAS;AACrD,eAAK,gBAAgB;AACrB,eAAK,SAAS;AACd,eAAK,aAAa,KAAK;AAAA,QAC3B,CAAC;AACD,aAAK,UAAU,EAAE,SAAS,cAAc,SAAS,kBAAkB,CAAC;AAAA,MACxE,SACO,OAAO;AACV,aAAK,MAAM,oCAAoC,KAAK,EAAE;AAAA,MAC1D;AAAA,IACJ,OACK;AACD,UAAI,KAAK,WAAW,eAAe,iBAAiB,cAChD,KAAK,WAAW,eAAe,iBAAiB,MAAM;AACtD,aAAK,gBAAgB;AAAA,MACzB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,WAAW;AACP,SAAK,aAAa;AAClB,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,KAAK;AAClB,WAAK,UAAU;AAAA,IACnB;AACA,QAAI,KAAK,SAAS;AACd,oBAAc,KAAK,OAAO;AAC1B,WAAK,UAAU;AAAA,IACnB;AAAA,EACJ;AAAA,EACA,QAAQ,QAAQ;AACZ,UAAM,EAAE,aAAa,SAAS,MAAM,YAAY,wBAAwB,IAAI;AAC5E,UAAM,OAAO,OAAO,OAAO,EAAE,YAAY,GAAG,OAAO;AACnD,SAAK,UAAU;AAAA,MACX,SAAS;AAAA,MACT,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,gBAAgB,WAAW,UAAU;AACjC,SAAK,iBAAiB,SAAS,IAAI;AAAA,EACvC;AAAA,EACA,UAAU,aAAa,UAAU,UAAU,CAAC,GAAG;AAC3C,cAAU,OAAO,OAAO,CAAC,GAAG,OAAO;AACnC,QAAI,CAAC,QAAQ,IAAI;AACb,cAAQ,KAAK,OAAO,KAAK,UAAU;AAAA,IACvC;AACA,YAAQ,cAAc;AACtB,SAAK,eAAe,QAAQ,EAAE,IAAI;AAClC,SAAK,UAAU,EAAE,SAAS,aAAa,QAAQ,CAAC;AAChD,UAAM,SAAS;AACf,WAAO;AAAA,MACH,IAAI,QAAQ;AAAA,MACZ,YAAY,MAAM;AACd,eAAO,OAAO,YAAY,QAAQ,IAAI,IAAI;AAAA,MAC9C;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,YAAY,IAAI,UAAU,CAAC,GAAG;AAC1B,cAAU,OAAO,OAAO,CAAC,GAAG,OAAO;AACnC,WAAO,KAAK,eAAe,EAAE;AAC7B,YAAQ,KAAK;AACb,SAAK,UAAU,EAAE,SAAS,eAAe,QAAQ,CAAC;AAAA,EACtD;AAAA,EACA,MAAM,eAAe;AACjB,UAAM,OAAO,iBAAiB,MAAM,KAAK,UAAU;AACnD,SAAK,UAAU;AAAA,MACX,SAAS;AAAA,MACT,SAAS;AAAA,QACL,aAAa;AAAA,MACjB;AAAA,IACJ,CAAC;AACD,UAAM,SAAS;AACf,WAAO;AAAA,MACH,IAAI;AAAA,MACJ,SAAS;AACL,eAAO,OAAO,IAAI;AAAA,MACtB;AAAA,MACA,QAAQ;AACJ,eAAO,MAAM,IAAI;AAAA,MACrB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,OAAO,eAAe;AAClB,SAAK,UAAU;AAAA,MACX,SAAS;AAAA,MACT,SAAS;AAAA,QACL,aAAa;AAAA,MACjB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,MAAM,eAAe;AACjB,SAAK,UAAU;AAAA,MACX,SAAS;AAAA,MACT,SAAS;AAAA,QACL,aAAa;AAAA,MACjB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,IAAI,WAAW,gBAAgB,UAAU,CAAC,GAAG;AACzC,cAAU,OAAO,OAAO,CAAC,GAAG,OAAO;AACnC,QAAI,KAAK,sBAAsB,SAAS,MAAM;AAC1C,cAAQ,KAAK;AAAA,IACjB,OACK;AACD,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,YAAQ,eAAe;AACvB,SAAK,UAAU,EAAE,SAAS,OAAO,QAAQ,CAAC;AAAA,EAC9C;AAAA,EACA,KAAK,WAAW,gBAAgB,UAAU,CAAC,GAAG;AAC1C,cAAU,OAAO,OAAO,CAAC,GAAG,OAAO;AACnC,QAAI,KAAK,sBAAsB,SAAS,MAAM;AAC1C,cAAQ,KAAK;AAAA,IACjB,OACK;AACD,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,YAAQ,eAAe;AACvB,WAAO,KAAK,UAAU,EAAE,SAAS,QAAQ,QAAQ,CAAC;AAAA,EACtD;AACJ;;;AC/XO,IAAM,SAAN,MAAa;AAAA;AAAA;AAAA;AAAA,EAIhB,IAAI,YAAY;AACZ,WAAO,KAAK,eAAe;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,oBAAoB;AACpB,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,kBAAkB,OAAO;AACzB,SAAK,qBAAqB;AAC1B,QAAI,KAAK,eAAe;AACpB,WAAK,cAAc,oBAAoB,KAAK;AAAA,IAChD;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,YAAY;AACZ,WAAO,CAAC,CAAC,KAAK,iBAAiB,KAAK,cAAc;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,mBAAmB;AACnB,WAAO,KAAK,gBAAgB,KAAK,cAAc,mBAAmB;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS;AACT,WAAO,KAAK,UAAU,gBAAgB;AAAA,EAC1C;AAAA,EACA,aAAa,OAAO;AAChB,SAAK,QAAQ;AACb,SAAK,cAAc,KAAK;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,OAAO,CAAC,GAAG;AAUnB,SAAK,gBAAgB,SAAS;AAK9B,SAAK,oBAAoB;AAIzB,SAAK,iBAAiB;AAKtB,SAAK,sBAAsB;AAM3B,SAAK,oBAAoB,KAAK,KAAK;AAanC,SAAK,oBAAoB,qBAAqB;AAI9C,SAAK,oBAAoB;AAIzB,SAAK,oBAAoB;AAgBzB,SAAK,oBAAoB,eAAe;AAcxC,SAAK,mBAAmB;AAKxB,SAAK,wBAAwB,IAAI;AASjC,SAAK,sBAAsB;AAW3B,SAAK,8BAA8B;AASnC,SAAK,gCAAgC;AAOrC,SAAK,QAAQ,gBAAgB;AAE7B,UAAM,OAAO,MAAM;AAAA,IAAE;AACrB,SAAK,QAAQ;AACb,SAAK,gBAAgB;AACrB,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,qBAAqB;AAC1B,SAAK,qBAAqB;AAC1B,SAAK,mBAAmB;AACxB,SAAK,eAAe;AACpB,SAAK,mBAAmB;AACxB,SAAK,mBAAmB;AACxB,SAAK,sBAAsB;AAC3B,SAAK,gBAAgB;AAErB,SAAK,iBAAiB,CAAC;AACvB,SAAK,qBAAqB,CAAC;AAE3B,SAAK,UAAU,IAAI;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,MAAM;AAEZ,WAAO,OAAO,MAAM,IAAI;AAExB,QAAI,KAAK,oBAAoB,KACzB,KAAK,oBAAoB,KAAK,gBAAgB;AAC9C,WAAK,MAAM,+BAA+B,KAAK,iBAAiB,oCAAoC,KAAK,cAAc,2DAA2D;AAClL,WAAK,oBAAoB,KAAK;AAAA,IAClC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,WAAW;AACP,UAAM,YAAY,MAAM;AACpB,UAAI,KAAK,QAAQ;AACb,aAAK,MAAM,8CAA8C;AACzD;AAAA,MACJ;AACA,WAAK,aAAa,gBAAgB,MAAM;AACxC,WAAK,sBAAsB,KAAK;AAChC,WAAK,SAAS;AAAA,IAClB;AAEA,QAAI,KAAK,UAAU,gBAAgB,cAAc;AAC7C,WAAK,MAAM,sDAAsD;AACjE,WAAK,WAAW,EAAE,KAAK,MAAM;AACzB,kBAAU;AAAA,MACd,CAAC;AAAA,IACL,OACK;AACD,gBAAU;AAAA,IACd;AAAA,EACJ;AAAA,EACM,WAAW;AAAA;AACb,YAAM,KAAK,cAAc,IAAI;AAC7B,UAAI,KAAK,eAAe;AACpB,aAAK,MAAM,+DAA+D;AAC1E;AAAA,MACJ;AACA,UAAI,CAAC,KAAK,QAAQ;AACd,aAAK,MAAM,8DAA8D;AACzE;AAAA,MACJ;AAEA,UAAI,KAAK,oBAAoB,GAAG;AAE5B,YAAI,KAAK,oBAAoB;AACzB,uBAAa,KAAK,kBAAkB;AAAA,QACxC;AACA,aAAK,qBAAqB,WAAW,MAAM;AACvC,cAAI,KAAK,WAAW;AAChB;AAAA,UACJ;AAGA,eAAK,MAAM,iCAAiC,KAAK,iBAAiB,oBAAoB;AACtF,eAAK,gBAAgB;AAAA,QACzB,GAAG,KAAK,iBAAiB;AAAA,MAC7B;AACA,WAAK,MAAM,uBAAuB;AAElC,YAAM,YAAY,KAAK,iBAAiB;AACxC,WAAK,gBAAgB,IAAI,aAAa,MAAM,WAAW;AAAA,QACnD,OAAO,KAAK;AAAA,QACZ,eAAe,KAAK;AAAA,QACpB,gBAAgB,KAAK;AAAA,QACrB,mBAAmB,KAAK;AAAA,QACxB,mBAAmB,KAAK;AAAA,QACxB,mBAAmB,KAAK;AAAA,QACxB,mBAAmB,KAAK;AAAA,QACxB,kBAAkB,KAAK;AAAA,QACvB,uBAAuB,KAAK;AAAA,QAC5B,qBAAqB,KAAK;AAAA,QAC1B,qBAAqB,KAAK;AAAA,QAC1B,6BAA6B,KAAK;AAAA,QAClC,+BAA+B,KAAK;AAAA,QACpC,WAAW,WAAS;AAEhB,cAAI,KAAK,oBAAoB;AACzB,yBAAa,KAAK,kBAAkB;AACpC,iBAAK,qBAAqB;AAAA,UAC9B;AACA,cAAI,CAAC,KAAK,QAAQ;AACd,iBAAK,MAAM,sEAAsE;AACjF,iBAAK,qBAAqB;AAC1B;AAAA,UACJ;AACA,eAAK,UAAU,KAAK;AAAA,QACxB;AAAA,QACA,cAAc,WAAS;AACnB,eAAK,aAAa,KAAK;AAAA,QAC3B;AAAA,QACA,cAAc,WAAS;AACnB,eAAK,aAAa,KAAK;AAAA,QAC3B;AAAA,QACA,kBAAkB,SAAO;AACrB,eAAK,gBAAgB;AACrB,cAAI,KAAK,UAAU,gBAAgB,cAAc;AAE7C,iBAAK,aAAa,gBAAgB,QAAQ;AAAA,UAC9C;AAGA,eAAK,iBAAiB,GAAG;AACzB,cAAI,KAAK,QAAQ;AACb,iBAAK,oBAAoB;AAAA,UAC7B;AAAA,QACJ;AAAA,QACA,kBAAkB,SAAO;AACrB,eAAK,iBAAiB,GAAG;AAAA,QAC7B;AAAA,QACA,oBAAoB,aAAW;AAC3B,eAAK,mBAAmB,OAAO;AAAA,QACnC;AAAA,QACA,oBAAoB,WAAS;AACzB,eAAK,mBAAmB,KAAK;AAAA,QACjC;AAAA,QACA,kBAAkB,WAAS;AACvB,eAAK,iBAAiB,KAAK;AAAA,QAC/B;AAAA,MACJ,CAAC;AACD,WAAK,cAAc,MAAM;AAAA,IAC7B;AAAA;AAAA,EACA,mBAAmB;AACf,QAAI;AACJ,QAAI,KAAK,kBAAkB;AACvB,kBAAY,KAAK,iBAAiB;AAAA,IACtC,WACS,KAAK,WAAW;AACrB,kBAAY,IAAI,UAAU,KAAK,WAAW,KAAK,cAAc,iBAAiB,CAAC;AAAA,IACnF,OACK;AACD,YAAM,IAAI,MAAM,uDAAuD;AAAA,IAC3E;AACA,cAAU,aAAa;AACvB,WAAO;AAAA,EACX;AAAA,EACA,sBAAsB;AAClB,QAAI,KAAK,sBAAsB,GAAG;AAC9B,WAAK,MAAM,qCAAqC,KAAK,mBAAmB,IAAI;AAC5E,WAAK,eAAe,WAAW,MAAM;AACjC,YAAI,KAAK,sBAAsB,qBAAqB,aAAa;AAC7D,eAAK,sBAAsB,KAAK,sBAAsB;AAEtD,cAAI,KAAK,sBAAsB,GAAG;AAC9B,iBAAK,sBAAsB,KAAK,IAAI,KAAK,qBAAqB,KAAK,iBAAiB;AAAA,UACxF;AAAA,QACJ;AACA,aAAK,SAAS;AAAA,MAClB,GAAG,KAAK,mBAAmB;AAAA,IAC/B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwBM,aAAyB;AAAA,+CAAd,UAAU,CAAC,GAAG;AAC3B,YAAM,QAAQ,QAAQ,SAAS;AAC/B,YAAM,gBAAgB,KAAK;AAC3B,UAAI;AACJ,UAAI,KAAK,UAAU,gBAAgB,UAAU;AACzC,aAAK,MAAM,sCAAsC;AACjD,eAAO,QAAQ,QAAQ;AAAA,MAC3B;AACA,WAAK,aAAa,gBAAgB,YAAY;AAE9C,WAAK,sBAAsB;AAE3B,UAAI,KAAK,cAAc;AACnB,qBAAa,KAAK,YAAY;AAC9B,aAAK,eAAe;AAAA,MACxB;AACA,UAAI,KAAK;AAAA,MAEL,KAAK,UAAU,eAAe,iBAAiB,QAAQ;AACvD,cAAM,uBAAuB,KAAK,cAAc;AAEhD,qBAAa,IAAI,QAAQ,CAAC,SAAS,WAAW;AAE1C,eAAK,cAAc,mBAAmB,SAAO;AACzC,iCAAqB,GAAG;AACxB,oBAAQ;AAAA,UACZ;AAAA,QACJ,CAAC;AAAA,MACL,OACK;AAED,aAAK,aAAa,gBAAgB,QAAQ;AAC1C,eAAO,QAAQ,QAAQ;AAAA,MAC3B;AACA,UAAI,OAAO;AACP,aAAK,eAAe,iBAAiB;AAAA,MACzC,WACS,eAAe;AACpB,aAAK,qBAAqB;AAAA,MAC9B;AACA,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB;AACd,QAAI,KAAK,eAAe;AACpB,WAAK,cAAc,gBAAgB;AAAA,IACvC;AAAA,EACJ;AAAA,EACA,uBAAuB;AAEnB,QAAI,KAAK,eAAe;AACpB,WAAK,cAAc,QAAQ;AAAA,IAC/B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqCA,QAAQ,QAAQ;AACZ,SAAK,iBAAiB;AAEtB,SAAK,cAAc,QAAQ,MAAM;AAAA,EACrC;AAAA,EACA,mBAAmB;AACf,QAAI,CAAC,KAAK,WAAW;AACjB,YAAM,IAAI,UAAU,yCAAyC;AAAA,IACjE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoCA,gBAAgB,WAAW,UAAU;AACjC,SAAK,iBAAiB;AAEtB,SAAK,cAAc,gBAAgB,WAAW,QAAQ;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyBA,UAAU,aAAa,UAAU,UAAU,CAAC,GAAG;AAC3C,SAAK,iBAAiB;AAEtB,WAAO,KAAK,cAAc,UAAU,aAAa,UAAU,OAAO;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,YAAY,IAAI,UAAU,CAAC,GAAG;AAC1B,SAAK,iBAAiB;AAEtB,SAAK,cAAc,YAAY,IAAI,OAAO;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,eAAe;AACjB,SAAK,iBAAiB;AAEtB,WAAO,KAAK,cAAc,MAAM,aAAa;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAO,eAAe;AAClB,SAAK,iBAAiB;AAEtB,SAAK,cAAc,OAAO,aAAa;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,eAAe;AACjB,SAAK,iBAAiB;AAEtB,SAAK,cAAc,MAAM,aAAa;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,IAAI,WAAW,gBAAgB,UAAU,CAAC,GAAG;AACzC,SAAK,iBAAiB;AAEtB,SAAK,cAAc,IAAI,WAAW,gBAAgB,OAAO;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,KAAK,WAAW,gBAAgB,UAAU,CAAC,GAAG;AAC1C,SAAK,iBAAiB;AAEtB,SAAK,cAAc,KAAK,WAAW,gBAAgB,OAAO;AAAA,EAC9D;AACJ;;;ACnoBO,IAAM,cAAN,MAAkB;AACzB;;;ACCO,IAAM,eAAN,MAAmB;AAC1B;;;ACLO,IAAM,gBAAN,MAAoB;AAAA,EACvB,YAAY,QAAQ;AAChB,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,IAAI,WAAW;AACX,WAAO,KAAK,OAAO;AAAA,EACvB;AAAA,EACA,IAAI,SAAS,OAAO;AAChB,SAAK,OAAO,oBAAoB;AAAA,EACpC;AAAA,EACA,IAAI,WAAW;AACX,WAAO,KAAK,OAAO;AAAA,EACvB;AAAA,EACA,IAAI,SAAS,OAAO;AAChB,SAAK,OAAO,oBAAoB;AAAA,EACpC;AACJ;;;ACVO,IAAM,eAAN,cAA2B,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASrC,YAAY,kBAAkB;AAC1B,UAAM;AAIN,SAAK,wBAAwB,KAAK;AAClC,SAAK,iBAAiB,IAAI,cAAc,IAAI;AAC5C,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AAExB,SAAK,QAAQ,IAAI,YAAY;AACzB,cAAQ,IAAI,GAAG,OAAO;AAAA,IAC1B;AAAA,EACJ;AAAA,EACA,iBAAiB,MAAM;AACnB,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,UAAU,CAAC;AACf,QAAI,KAAK,SAAS,GAAG;AACjB,YAAM,IAAI,MAAM,uCAAuC;AAAA,IAC3D;AACA,QAAI,OAAO,KAAK,CAAC,MAAM,YAAY;AAC/B,OAAC,SAAS,iBAAiB,eAAe,kBAAkB,IAAI;AAAA,IACpE,OACK;AACD,cAAQ,KAAK,QAAQ;AAAA,QACjB,KAAK;AACD;AAAA,YACI,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,YACA,QAAQ;AAAA,UACZ,IAAI;AACJ;AAAA,QACJ;AACI;AAAA,YACI,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACJ,IAAI;AAAA,MACZ;AAAA,IACJ;AACA,WAAO,CAAC,SAAS,iBAAiB,eAAe,kBAAkB;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4BA,WAAW,MAAM;AACb,UAAM,MAAM,KAAK,cAAc,GAAG,IAAI;AACtC,QAAI,IAAI,CAAC,GAAG;AACR,WAAK,iBAAiB,IAAI,CAAC;AAAA,IAC/B;AACA,QAAI,IAAI,CAAC,GAAG;AACR,WAAK,YAAY,IAAI,CAAC;AAAA,IAC1B;AACA,QAAI,IAAI,CAAC,GAAG;AACR,WAAK,eAAe,IAAI,CAAC;AAAA,IAC7B;AACA,QAAI,IAAI,CAAC,GAAG;AACR,WAAK,mBAAmB,IAAI,CAAC;AAAA,IACjC;AACA,UAAM,SAAS;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,WAAW,oBAAoB,UAAU,CAAC,GAAG;AACzC,QAAI,oBAAoB;AACpB,WAAK,eAAe;AAAA,IACxB;AACA,SAAK,oBAAoB;AACzB,UAAM,WAAW;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,KAAK,aAAa,UAAU,CAAC,GAAG,OAAO,IAAI;AACvC,cAAU,OAAO,OAAO,CAAC,GAAG,OAAO;AACnC,UAAM,0BAA0B,QAAQ,gBAAgB,MAAM;AAC9D,QAAI,yBAAyB;AACzB,aAAO,QAAQ,gBAAgB;AAAA,IACnC;AACA,SAAK,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,gBAAgB,OAAO;AACvB,SAAK,iBAAiB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,KAAK;AACL,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,UAAU;AACV,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,YAAY;AACZ,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,UAAU,OAAO;AACjB,SAAK,qBAAqB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,YAAY;AACZ,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,UAAU,OAAO;AACjB,SAAK,qBAAqB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,YAAY;AACZ,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,UAAU,OAAO;AACjB,SAAK,oBAAoB,MAAM;AAC/B,SAAK,oBAAoB,MAAM;AAAA,EACnC;AACJ;;;ACpOO,IAAM,QAAN,MAAM,OAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAef,OAAO,OAAO,KAAK,WAAW;AAa1B,QAAI,aAAa,MAAM;AACnB,kBAAY,SAAS,QAAQ,iBAAiB;AAAA,IAClD;AACA,UAAM,OAAO,MAAM;AACf,YAAM,QAAQ,OAAM,kBAAkB;AACtC,aAAO,IAAI,MAAM,KAAK,SAAS;AAAA,IACnC;AACA,WAAO,IAAI,aAAa,IAAI;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBA,OAAO,KAAK,IAAI;AACZ,QAAI;AACJ,QAAI,OAAO,OAAO,YAAY;AAC1B,aAAO;AAAA,IACX,OACK;AACD,cAAQ,KAAK,mJACsE;AACnF,aAAO,MAAM;AAAA,IACjB;AACA,WAAO,IAAI,aAAa,IAAI;AAAA,EAChC;AACJ;AAmBA,MAAM,iBAAiB;", "names": ["StompSocketState", "ActivationState", "ReconnectionTimeMode", "TickerStrategy"]}