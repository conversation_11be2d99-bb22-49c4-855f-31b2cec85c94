<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Connexion</title>
  <link rel="stylesheet" href="utilisateur.component.css" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet" />
</head>
<body>

  <form #loginForm="ngForm" (ngSubmit)="onLogin()" novalidate>
    <div class="login-container">
      <div class="login-header">
        <img src="assets/images/logos/esprit.png" alt="" style="width: 200px; height: auto; display: block; margin-left: 0px;" />
    
      </div>

      <div class="login-box">
        <h2>Authentification Sécurisée</h2>
        <p class="subtitle">Choisissez votre méthode de connexion préférée</p>

        <!-- Email -->
        <label for="email">Adresse email</label>
        <div class="input-group">
          <span class="icon">&#64;</span>
          <input
            type="email"
            [(ngModel)]="email"
            name="email"
            id="email"
            placeholder="<EMAIL>"
            required
            email
            #emailRef="ngModel"
          />
        </div>
        <div *ngIf="emailRef.invalid && emailRef.touched" class="error-message">
          <span *ngIf="emailRef.errors?.['required']">L'email est requis.</span>
          <span *ngIf="emailRef.errors?.['email']">Email invalide.</span>
        </div>

        <!-- Auth method -->
        <p class="auth-title">Méthode d'authentification</p>
        <div class="auth-methods">
          <button type="button"
                  class="auth-btn"
                  [ngClass]="{ active: methode === 'password' }"
                  (click)="setMethode('password')">
            🔒 Mot de passe
          </button>
          <button type="button"
                  class="auth-btn"
                  [ngClass]="{ active: methode === 'face' }"
                  (click)="setMethode('face')">
            📷 Reconnaissance faciale
          </button>
        </div>

        <!-- Mot de passe -->
        <div class="password-box" *ngIf="methode === 'password'">
          <label for="password">Mot de passe</label>
          <input
            type="password"
            name="password"
            id="password"
            [(ngModel)]="password"
            placeholder="Entrez votre mot de passe"
            required
            minlength="6"
            #passwordRef="ngModel"
          />
          <div *ngIf="passwordRef.invalid && passwordRef.touched" class="error-message">
            <span *ngIf="passwordRef.errors?.['required']">Le mot de passe est requis.</span>
            <span *ngIf="passwordRef.errors?.['minlength']">
              Le mot de passe doit contenir au moins 6 caractères.
            </span>
          </div>
        </div>

<div class="face-box" *ngIf="methode === 'face'">
  <!-- Webcam + face detection -->
  <div class="face-frame">
    <webcam
      class="my-webcam"
      [width]="400"
      [height]="350"
      [trigger]="triggerObservable"
      (imageCapture)="handleImage($event)">
    </webcam>

    <p>Positionnez votre visage et cliquez pour vérifier</p>

<button (click)="triggerSnapshot()" style="color: white;">
  Démarrer la vérification
</button>



    <p *ngIf="resultMessage">{{ resultMessage }}</p>
  </div>
</div>



        <!-- Bouton Connexion -->
        <button class="connect-btn" type="submit" >Se connecter</button>
<!-- Message d'erreur global si les identifiants sont invalides -->
 <p *ngIf="errorMessage"
             class="error-message"
             style="color: red; margin-top: 10px; margin-bottom: 15px; font-size: 18px;">
            {{ errorMessage }}
          </p>

        <div class="forgot-link">
          <a [routerLink]="['/motpasseoublie']">Mot de passe oublié ?</a>
        </div>

        <div class="admin-setup-link" style="text-align: center; margin-top: 15px;">
          <a [routerLink]="['/admin-setup']" style="color: #666; font-size: 14px; text-decoration: none;">
            🔧 Configuration initiale du système
          </a>
        </div>
      </div>
    </div>
  </form>

</body>
</html>
