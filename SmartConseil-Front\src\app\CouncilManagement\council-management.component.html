<!-- Council Management Component -->
  <!--  Body Wrapper -->
  <div class="page-wrapper" id="main-wrapper" data-layout="vertical" data-navbarbg="skin6" data-sidebartype="full"
    data-sidebar-position="fixed" data-header-position="fixed">
    <!-- Sidebar Start -->
    <aside class="left-sidebar">
      <!-- Sidebar scroll-->
      <div>
        <div class="brand-logo d-flex align-items-center justify-content-between">
          <a href="./index.html" class="text-nowrap logo-img">
            <img src="assets/images/logos/dark-logo.svg" width="180" alt="" />
          </a>
          <div class="close-btn d-xl-none d-block sidebartoggler cursor-pointer" id="sidebarCollapse">
            <i class="ti ti-x fs-8"></i>
          </div>
        </div>

        <!-- Sidebar navigation-->
        <nav class="sidebar-nav scroll-sidebar" data-simplebar="">
          <ul id="sidebarnav">
            <li class="nav-small-cap">
              <span class="hide-menu">Home</span>
            </li>

            <li class="sidebar-item">
              <a class="sidebar-link" href="./index.html" aria-expanded="false">
                <i class="ti ti-atom"></i>
                <span class="hide-menu">Dashboard</span>
              </a>
            </li>

            <li class="sidebar-item">
              <a class="sidebar-link" href="./index.html" aria-expanded="false">
                <i class="ti ti-atom"></i>
                <span class="hide-menu">Rectification des Notes</span>
              </a>
            </li>

            <li class="sidebar-item">
              <a class="sidebar-link" href="./index.html" aria-expanded="false">
                <i class="ti ti-atom"></i>
                <span class="hide-menu">Planification Conseils</span>
              </a>
            </li>

            <li class="sidebar-item">
              <a class="sidebar-link" href="./index.html" aria-expanded="false">
                <i class="ti ti-users"></i>
                <span class="hide-menu">Gestion des Conseils</span>
              </a>
            </li>

            <li class="nav-small-cap">
              <span class="hide-menu">AUTH</span>
            </li>
            <li class="sidebar-item">
              <a class="sidebar-link" href="./authentication-login.html" aria-expanded="false">
                <i class="ti ti-login"></i>
                <span class="hide-menu">Login</span>
              </a>
            </li>
            <li class="sidebar-item">
              <a class="sidebar-link" href="./authentication-register.html" aria-expanded="false">
                <i class="ti ti-user-plus"></i>
                <span class="hide-menu">Register</span>
              </a>
            </li>
          </ul>
        </nav>
        <!-- End Sidebar navigation -->
      </div>
      <!-- End Sidebar scroll-->
    </aside>

    <!--  Sidebar End -->
    <!--  Main wrapper -->
    <div class="body-wrapper">
      <!--  Header Start -->
      <header class="app-header">

      </header>
      <!--  Header End -->
      <div class="body-wrapper-inner">
        <div class="container-fluid">
          <div class="welcome-header">
            <h1>Bienvenue dans votre espace de gestion des conseils</h1>
            <p>Gérez efficacement les conseils de classe et les sessions de jury</p>
          </div>

          <!-- Header with action button -->
          <div class="header-container">
            <div class="header-text">
              <h2>Gestion des Conseils</h2>
              <p>Module 5 - Gestion des Conseils de Classe</p>
            </div>
            <button class="add-user-btn" (click)="openTokenModal()" *ngIf="userRole === 'teacher'">
              <span class="icon">🔑</span> Accéder au Conseil
            </button>
            <button class="add-user-btn" (click)="startCouncilSession()" *ngIf="userRole === 'jury_president'">
              <span class="icon">▶️</span> Démarrer Session
            </button>
          </div>

          <style>
            .body {
              font-family: 'Arial', sans-serif;
              background: #f5f5f5;
              padding: 30px;
            }

            .card {
              background: #fff;
              width: 1100px;
              margin: auto;
              padding: 20px;
              border-radius: 12px;
              border: 1px solid #e5e7eb;
              box-shadow: 0 2px 5px rgba(0,0,0,0.05);
              margin-bottom: 25px;
            }

            .header {
              display: flex;
              align-items: center;
              font-weight: bold;
              font-size: 16px;
              margin-bottom: 15px;
            }

            .header span {
              margin-left: 10px;
            }

            .status {
              margin-left: auto;
              padding: 4px 12px;
              border-radius: 999px;
              font-size: 13px;
              font-weight: 600;
            }

            .status.active {
              background-color: #d1fae5;
              color: #065f46;
            }

            .status.closed {
              background-color: #fee2e2;
              color: #991b1b;
            }

            .status.pending {
              background-color: #fef3c7;
              color: #92400e;
            }

            .info-line {
              display: flex;
              justify-content: space-between;
              margin: 10px 0;
            }

            .info-block {
              flex: 1;
              text-align: center;
              font-size: 14px;
              color: #374151;
            }

            .stats-grid {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 20px;
              margin: 20px 0;
            }

            .stat-card {
              background: white;
              padding: 20px;
              border-radius: 10px;
              box-shadow: 0 2px 5px rgba(0,0,0,0.05);
              text-align: center;
            }

            .stat-value {
              font-size: 24px;
              font-weight: bold;
              color: #2563eb;
            }

            .stat-label {
              font-size: 14px;
              color: #6b7280;
              margin-top: 5px;
            }

            .action-buttons {
              display: flex;
              gap: 10px;
              margin-top: 15px;
            }

            .btn {
              padding: 8px 16px;
              border: none;
              border-radius: 6px;
              cursor: pointer;
              font-size: 14px;
              transition: all 0.2s;
            }

            .btn-primary {
              background-color: #2563eb;
              color: white;
            }

            .btn-secondary {
              background-color: #6b7280;
              color: white;
            }

            .btn-success {
              background-color: #059669;
              color: white;
            }

            .btn-warning {
              background-color: #d97706;
              color: white;
            }

            .btn:hover {
              opacity: 0.9;
              transform: translateY(-1px);
            }

            .modal {
              display: none;
              position: fixed;
              z-index: 1000;
              left: 0;
              top: 0;
              width: 100%;
              height: 100%;
              background-color: rgba(0,0,0,0.5);
            }

            .modal.show {
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .modal-content {
              background-color: white;
              padding: 30px;
              border-radius: 10px;
              width: 400px;
              max-width: 90%;
            }

            .form-group {
              margin-bottom: 20px;
            }

            .form-group label {
              display: block;
              margin-bottom: 5px;
              font-weight: bold;
            }

            .form-group input, .form-group select, .form-group textarea {
              width: 100%;
              padding: 10px;
              border: 1px solid #d1d5db;
              border-radius: 6px;
              font-size: 14px;
            }

            .top-students {
              background: #f8fafc;
              padding: 15px;
              border-radius: 8px;
              margin: 10px 0;
            }

            .student-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 0;
              border-bottom: 1px solid #e5e7eb;
            }

            .student-item:last-child {
              border-bottom: none;
            }

            .vote-section {
              background: #fef7ff;
              padding: 15px;
              border-radius: 8px;
              margin: 10px 0;
            }

            .vote-buttons {
              display: flex;
              gap: 10px;
              margin-top: 10px;
            }
          </style>

          <!--  Row 1 -->
          <div class="row">
            <div class="container">
              <!-- Council Session Status -->
              <div class="calendar">
                <h2>
                  <img src="assets/images/logos/calendar.JPG" alt="Conseil"
                    style="width: 40px; height: 40px; vertical-align: middle; margin-right: 8px;">
                  État des Sessions de Conseil
                </h2>

                <p class="week">
                  Session en cours - {{ currentDate | date: 'dd MMMM yyyy' }}
                </p>

                <!-- Council Sessions List -->
                <div *ngFor="let session of councilSessions" class="event"
                     [ngClass]="{'confirmed': session.status === 'active', 'pending': session.status === 'pending', 'closed': session.status === 'closed'}">
                  <strong>Classe : {{ session.className }}</strong>

                  <div style="font-size: 0.85rem; color: #555; display: flex; align-items: center; gap: 8px; margin-top: 4px;">
                    <span *ngIf="session.startTime">Démarré à {{ session.startTime }}</span>
                    <span *ngIf="session.startTime && session.room" style="border-left: 1px solid #555; height: 12px;"></span>
                    <span *ngIf="session.room">Salle {{ session.room }}</span>
                    <span *ngIf="session.token && userRole === 'teacher'" style="border-left: 1px solid #555; height: 12px;"></span>
                    <span *ngIf="session.token && userRole === 'teacher'">Token: {{ session.token }}</span>
                  </div>

                  <span class="status" [ngClass]="session.status">
                    {{ getStatusLabel(session.status) }}
                  </span>
                </div>

                <div *ngIf="councilSessions.length === 0">
                  <p>Aucune session de conseil active.</p>
                </div>
              </div>

              <!-- Quick Actions -->
              <div class="actions">
                <h2>Actions Rapides</h2>
                <p class="subtitle">Outils de gestion des conseils</p>
                
                <button *ngIf="userRole === 'jury_president'">
                  <img src="assets/images/logos/calendar.JPG" style="width: 20px; height: 20px; vertical-align: middle; margin-right: 5px;">
                  Générer statistiques
                </button>
                
                <button *ngIf="userRole === 'jury_president'">
                  <img src="assets/images/logos/position.JPG" style="width: 23px; height: 25px; vertical-align: middle; margin-right: 5px;">
                  Cas spéciaux
                </button>

                <button *ngIf="userRole === 'teacher'">
                  <img src="assets/images/logos/mennn.JPG" style="width: 23px; height: 25px; vertical-align: middle; margin-right: 5px;">
                  Justifier absence
                </button>
                
                <button>
                  <img src="assets/images/logos/filter.JPG" style="width: 23px; height: 25px; vertical-align: middle; margin-right: 5px;">
                  Historique conseils
                </button>
              </div>
            </div>
          </div>

          <div class="big-card">
            <!-- Statistics Section for Jury President -->
            <div *ngIf="userRole === 'jury_president'">
              <h2>Statistiques des Conseils</h2>
              <div class="subtext">Aperçu des performances et cas spéciaux</div>

              <div class="stats-grid">
                <div class="stat-card">
                  <div class="stat-value">{{ statistics.successRate }}%</div>
                  <div class="stat-label">Taux de réussite</div>
                </div>
                <div class="stat-card">
                  <div class="stat-value">{{ statistics.below10Percent }}%</div>
                  <div class="stat-label">Moyenne < 10</div>
                </div>
                <div class="stat-card">
                  <div class="stat-value">{{ statistics.above15Percent }}%</div>
                  <div class="stat-label">Moyenne > 15</div>
                </div>
                <div class="stat-card">
                  <div class="stat-value">{{ statistics.classAverage }}</div>
                  <div class="stat-label">Moyenne générale</div>
                </div>
                <div class="stat-card">
                  <div class="stat-value">{{ statistics.specialCases }}</div>
                  <div class="stat-label">Cas spéciaux</div>
                </div>
              </div>
            </div>

            <!-- Council Management Cards -->
            <h2>Gestion des Conseils de Classe</h2>
            <div class="subtext">Liste des classes et sessions de conseil</div>

            <!-- Council Cards -->
            <div *ngFor="let council of councils" class="card">
              <div class="header">
                <img src="assets/images/logos/calendar.JPG" alt="Conseil"
                     style="width: 28px; height: 28px; vertical-align: middle; margin-right: 5px;">
                <span>{{ council.className }}</span>
                <span class="status" [ngClass]="council.status">{{ getStatusLabel(council.status) }}</span>
              </div>

              <!-- Top 3 Students Section -->
              <div class="top-students" *ngIf="council.topStudents && council.topStudents.length > 0">
                <h4>🏆 Top 3 Étudiants</h4>
                <div *ngFor="let student of council.topStudents; let i = index" class="student-item">
                  <span>{{ i + 1 }}. {{ student.name }}</span>
                  <span class="stat-value">{{ student.average }}/20</span>
                </div>
              </div>

              <!-- Special Cases Section -->
              <div class="top-students" *ngIf="council.specialCases && council.specialCases.length > 0">
                <h4>⚠️ Cas Spéciaux</h4>
                <div *ngFor="let case of council.specialCases" class="student-item">
                  <span>{{ case.studentName }} - {{ case.type }}</span>
                  <span [ngClass]="case.status === 'processed' ? 'btn-success' : 'btn-warning'"
                        style="padding: 2px 8px; border-radius: 4px; font-size: 12px;">
                    {{ case.status === 'processed' ? 'Traité' : 'En attente' }}
                  </span>
                </div>
              </div>

              <!-- Voting Section for Teachers -->
              <div class="vote-section" *ngIf="userRole === 'teacher' && council.status === 'active' && canVoteForClass(council.className)">
                <h4>🗳️ Vote pour les étudiants</h4>
                <div *ngFor="let student of getStudentsForVoting(council.className)" class="student-item">
                  <span>{{ student.name }} - {{ student.subject }}</span>
                  <div class="vote-buttons">
                    <button class="btn btn-success" (click)="voteForStudent(student.id, 'approve')"
                            [disabled]="hasVoted(student.id)">
                      ✓ Approuver
                    </button>
                    <button class="btn btn-warning" (click)="voteForStudent(student.id, 'reject')"
                            [disabled]="hasVoted(student.id)">
                      ✗ Rejeter
                    </button>
                  </div>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="action-buttons">
                <button class="btn btn-primary" *ngIf="userRole === 'jury_president' && council.status === 'pending'"
                        (click)="startCouncilForClass(council.className)">
                  ▶️ Démarrer Conseil
                </button>
                <button class="btn btn-secondary" *ngIf="userRole === 'jury_president' && council.status === 'active'"
                        (click)="closeCouncilForClass(council.className)">
                  ⏹️ Fermer Conseil
                </button>
                <button class="btn btn-primary" *ngIf="userRole === 'jury_president'"
                        (click)="viewCouncilDetails(council.className)">
                  📊 Détails
                </button>
                <button class="btn btn-warning" *ngIf="userRole === 'teacher'"
                        (click)="justifyAbsence(council.className)">
                  📝 Justifier Absence
                </button>
              </div>

              <!-- Council Rules Reminder -->
              <div class="top-students" *ngIf="council.status === 'active'">
                <h4>📋 Rappel des Règles</h4>
                <ul style="margin: 10px 0; padding-left: 20px; font-size: 14px;">
                  <li>Rachat sur moyenne générale: Moyenne ≥ 8/20</li>
                  <li>Rachat par matière/module: Note ≥ 7/20 dans la matière</li>
                  <li>Un seul vote par étudiant par module</li>
                  <li>Présence obligatoire des enseignants concernés</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Token Access Modal -->
          <div class="modal" [ngClass]="{'show': showTokenModal}">
            <div class="modal-content">
              <h3>Accès au Conseil</h3>
              <p>Veuillez entrer le token affiché dans la salle:</p>
              <div class="form-group">
                <label for="accessToken">Token d'accès:</label>
                <input type="text" id="accessToken" [(ngModel)]="accessToken"
                       placeholder="Entrez le token" maxlength="8">
              </div>
              <div class="action-buttons">
                <button class="btn btn-primary" (click)="validateToken()">Valider</button>
                <button class="btn btn-secondary" (click)="closeTokenModal()">Annuler</button>
              </div>
            </div>
          </div>

          <!-- Absence Justification Modal -->
          <div class="modal" [ngClass]="{'show': showAbsenceModal}">
            <div class="modal-content">
              <h3>Justifier votre Absence</h3>
              <div class="form-group">
                <label for="absenceReason">Motif d'absence:</label>
                <select id="absenceReason" [(ngModel)]="absenceReason">
                  <option value="">Sélectionnez un motif</option>
                  <option value="autre_conseil">Participation à un autre conseil</option>
                  <option value="jury_pfe">Jury de projet de fin d'études</option>
                  <option value="surveillance_examen">Surveillance d'examen</option>
                  <option value="validation_projet">Validation de projet</option>
                  <option value="maladie">Maladie</option>
                </select>
              </div>
              <div class="form-group">
                <label for="absenceDetails">Détails (optionnel):</label>
                <textarea id="absenceDetails" [(ngModel)]="absenceDetails"
                          rows="3" placeholder="Précisions supplémentaires..."></textarea>
              </div>
              <div class="action-buttons">
                <button class="btn btn-primary" (click)="submitAbsenceJustification()">Soumettre</button>
                <button class="btn btn-secondary" (click)="closeAbsenceModal()">Annuler</button>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
