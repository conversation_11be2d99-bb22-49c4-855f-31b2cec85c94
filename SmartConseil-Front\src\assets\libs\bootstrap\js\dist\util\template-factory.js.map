{"version": 3, "file": "template-factory.js", "sources": ["../../src/util/template-factory.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap util/template-factory.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine.js'\nimport Config from './config.js'\nimport { DefaultAllowlist, sanitizeHtml } from './sanitizer.js'\nimport { execute, getElement, isElement } from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'TemplateFactory'\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  content: {}, // { selector : text ,  selector2 : text2 , }\n  extraClass: '',\n  html: false,\n  sanitize: true,\n  sanitizeFn: null,\n  template: '<div></div>'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  content: 'object',\n  extraClass: '(string|function)',\n  html: 'boolean',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  template: 'string'\n}\n\nconst DefaultContentType = {\n  entry: '(string|element|function|null)',\n  selector: '(string|element)'\n}\n\n/**\n * Class definition\n */\n\nclass TemplateFactory extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  getContent() {\n    return Object.values(this._config.content)\n      .map(config => this._resolvePossibleFunction(config))\n      .filter(Boolean)\n  }\n\n  hasContent() {\n    return this.getContent().length > 0\n  }\n\n  changeContent(content) {\n    this._checkContent(content)\n    this._config.content = { ...this._config.content, ...content }\n    return this\n  }\n\n  toHtml() {\n    const templateWrapper = document.createElement('div')\n    templateWrapper.innerHTML = this._maybeSanitize(this._config.template)\n\n    for (const [selector, text] of Object.entries(this._config.content)) {\n      this._setContent(templateWrapper, text, selector)\n    }\n\n    const template = templateWrapper.children[0]\n    const extraClass = this._resolvePossibleFunction(this._config.extraClass)\n\n    if (extraClass) {\n      template.classList.add(...extraClass.split(' '))\n    }\n\n    return template\n  }\n\n  // Private\n  _typeCheckConfig(config) {\n    super._typeCheckConfig(config)\n    this._checkContent(config.content)\n  }\n\n  _checkContent(arg) {\n    for (const [selector, content] of Object.entries(arg)) {\n      super._typeCheckConfig({ selector, entry: content }, DefaultContentType)\n    }\n  }\n\n  _setContent(template, content, selector) {\n    const templateElement = SelectorEngine.findOne(selector, template)\n\n    if (!templateElement) {\n      return\n    }\n\n    content = this._resolvePossibleFunction(content)\n\n    if (!content) {\n      templateElement.remove()\n      return\n    }\n\n    if (isElement(content)) {\n      this._putElementInTemplate(getElement(content), templateElement)\n      return\n    }\n\n    if (this._config.html) {\n      templateElement.innerHTML = this._maybeSanitize(content)\n      return\n    }\n\n    templateElement.textContent = content\n  }\n\n  _maybeSanitize(arg) {\n    return this._config.sanitize ? sanitizeHtml(arg, this._config.allowList, this._config.sanitizeFn) : arg\n  }\n\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [this])\n  }\n\n  _putElementInTemplate(element, templateElement) {\n    if (this._config.html) {\n      templateElement.innerHTML = ''\n      templateElement.append(element)\n      return\n    }\n\n    templateElement.textContent = element.textContent\n  }\n}\n\nexport default TemplateFactory\n"], "names": ["NAME", "<PERSON><PERSON><PERSON>", "allowList", "DefaultAllowlist", "content", "extraClass", "html", "sanitize", "sanitizeFn", "template", "DefaultType", "DefaultContentType", "entry", "selector", "TemplateFactory", "Config", "constructor", "config", "_config", "_getConfig", "get<PERSON>ontent", "Object", "values", "map", "_resolvePossibleFunction", "filter", "Boolean", "<PERSON><PERSON><PERSON><PERSON>", "length", "changeContent", "_checkContent", "toHtml", "templateWrapper", "document", "createElement", "innerHTML", "_maybeSanitize", "text", "entries", "_setContent", "children", "classList", "add", "split", "_typeCheckConfig", "arg", "templateElement", "SelectorEngine", "findOne", "remove", "isElement", "_putElementInTemplate", "getElement", "textContent", "sanitizeHtml", "execute", "element", "append"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;;EAOA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,iBAAiB,CAAA;EAE9B,MAAMC,OAAO,GAAG;EACdC,EAAAA,SAAS,EAAEC,6BAAgB;IAC3BC,OAAO,EAAE,EAAE;EAAE;EACbC,EAAAA,UAAU,EAAE,EAAE;EACdC,EAAAA,IAAI,EAAE,KAAK;EACXC,EAAAA,QAAQ,EAAE,IAAI;EACdC,EAAAA,UAAU,EAAE,IAAI;EAChBC,EAAAA,QAAQ,EAAE,aAAA;EACZ,CAAC,CAAA;EAED,MAAMC,WAAW,GAAG;EAClBR,EAAAA,SAAS,EAAE,QAAQ;EACnBE,EAAAA,OAAO,EAAE,QAAQ;EACjBC,EAAAA,UAAU,EAAE,mBAAmB;EAC/BC,EAAAA,IAAI,EAAE,SAAS;EACfC,EAAAA,QAAQ,EAAE,SAAS;EACnBC,EAAAA,UAAU,EAAE,iBAAiB;EAC7BC,EAAAA,QAAQ,EAAE,QAAA;EACZ,CAAC,CAAA;EAED,MAAME,kBAAkB,GAAG;EACzBC,EAAAA,KAAK,EAAE,gCAAgC;EACvCC,EAAAA,QAAQ,EAAE,kBAAA;EACZ,CAAC,CAAA;;EAED;EACA;EACA;;EAEA,MAAMC,eAAe,SAASC,MAAM,CAAC;IACnCC,WAAWA,CAACC,MAAM,EAAE;EAClB,IAAA,KAAK,EAAE,CAAA;MACP,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,UAAU,CAACF,MAAM,CAAC,CAAA;EACxC,GAAA;;EAEA;IACA,WAAWhB,OAAOA,GAAG;EACnB,IAAA,OAAOA,OAAO,CAAA;EAChB,GAAA;IAEA,WAAWS,WAAWA,GAAG;EACvB,IAAA,OAAOA,WAAW,CAAA;EACpB,GAAA;IAEA,WAAWV,IAAIA,GAAG;EAChB,IAAA,OAAOA,IAAI,CAAA;EACb,GAAA;;EAEA;EACAoB,EAAAA,UAAUA,GAAG;MACX,OAAOC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACJ,OAAO,CAACd,OAAO,CAAC,CACvCmB,GAAG,CAACN,MAAM,IAAI,IAAI,CAACO,wBAAwB,CAACP,MAAM,CAAC,CAAC,CACpDQ,MAAM,CAACC,OAAO,CAAC,CAAA;EACpB,GAAA;EAEAC,EAAAA,UAAUA,GAAG;MACX,OAAO,IAAI,CAACP,UAAU,EAAE,CAACQ,MAAM,GAAG,CAAC,CAAA;EACrC,GAAA;IAEAC,aAAaA,CAACzB,OAAO,EAAE;EACrB,IAAA,IAAI,CAAC0B,aAAa,CAAC1B,OAAO,CAAC,CAAA;EAC3B,IAAA,IAAI,CAACc,OAAO,CAACd,OAAO,GAAG;EAAE,MAAA,GAAG,IAAI,CAACc,OAAO,CAACd,OAAO;QAAE,GAAGA,OAAAA;OAAS,CAAA;EAC9D,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;EAEA2B,EAAAA,MAAMA,GAAG;EACP,IAAA,MAAMC,eAAe,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAAA;EACrDF,IAAAA,eAAe,CAACG,SAAS,GAAG,IAAI,CAACC,cAAc,CAAC,IAAI,CAAClB,OAAO,CAACT,QAAQ,CAAC,CAAA;EAEtE,IAAA,KAAK,MAAM,CAACI,QAAQ,EAAEwB,IAAI,CAAC,IAAIhB,MAAM,CAACiB,OAAO,CAAC,IAAI,CAACpB,OAAO,CAACd,OAAO,CAAC,EAAE;QACnE,IAAI,CAACmC,WAAW,CAACP,eAAe,EAAEK,IAAI,EAAExB,QAAQ,CAAC,CAAA;EACnD,KAAA;EAEA,IAAA,MAAMJ,QAAQ,GAAGuB,eAAe,CAACQ,QAAQ,CAAC,CAAC,CAAC,CAAA;MAC5C,MAAMnC,UAAU,GAAG,IAAI,CAACmB,wBAAwB,CAAC,IAAI,CAACN,OAAO,CAACb,UAAU,CAAC,CAAA;EAEzE,IAAA,IAAIA,UAAU,EAAE;EACdI,MAAAA,QAAQ,CAACgC,SAAS,CAACC,GAAG,CAAC,GAAGrC,UAAU,CAACsC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAA;EAClD,KAAA;EAEA,IAAA,OAAOlC,QAAQ,CAAA;EACjB,GAAA;;EAEA;IACAmC,gBAAgBA,CAAC3B,MAAM,EAAE;EACvB,IAAA,KAAK,CAAC2B,gBAAgB,CAAC3B,MAAM,CAAC,CAAA;EAC9B,IAAA,IAAI,CAACa,aAAa,CAACb,MAAM,CAACb,OAAO,CAAC,CAAA;EACpC,GAAA;IAEA0B,aAAaA,CAACe,GAAG,EAAE;EACjB,IAAA,KAAK,MAAM,CAAChC,QAAQ,EAAET,OAAO,CAAC,IAAIiB,MAAM,CAACiB,OAAO,CAACO,GAAG,CAAC,EAAE;QACrD,KAAK,CAACD,gBAAgB,CAAC;UAAE/B,QAAQ;EAAED,QAAAA,KAAK,EAAER,OAAAA;SAAS,EAAEO,kBAAkB,CAAC,CAAA;EAC1E,KAAA;EACF,GAAA;EAEA4B,EAAAA,WAAWA,CAAC9B,QAAQ,EAAEL,OAAO,EAAES,QAAQ,EAAE;MACvC,MAAMiC,eAAe,GAAGC,cAAc,CAACC,OAAO,CAACnC,QAAQ,EAAEJ,QAAQ,CAAC,CAAA;MAElE,IAAI,CAACqC,eAAe,EAAE;EACpB,MAAA,OAAA;EACF,KAAA;EAEA1C,IAAAA,OAAO,GAAG,IAAI,CAACoB,wBAAwB,CAACpB,OAAO,CAAC,CAAA;MAEhD,IAAI,CAACA,OAAO,EAAE;QACZ0C,eAAe,CAACG,MAAM,EAAE,CAAA;EACxB,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,IAAIC,kBAAS,CAAC9C,OAAO,CAAC,EAAE;QACtB,IAAI,CAAC+C,qBAAqB,CAACC,mBAAU,CAAChD,OAAO,CAAC,EAAE0C,eAAe,CAAC,CAAA;EAChE,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,IAAI,IAAI,CAAC5B,OAAO,CAACZ,IAAI,EAAE;QACrBwC,eAAe,CAACX,SAAS,GAAG,IAAI,CAACC,cAAc,CAAChC,OAAO,CAAC,CAAA;EACxD,MAAA,OAAA;EACF,KAAA;MAEA0C,eAAe,CAACO,WAAW,GAAGjD,OAAO,CAAA;EACvC,GAAA;IAEAgC,cAAcA,CAACS,GAAG,EAAE;MAClB,OAAO,IAAI,CAAC3B,OAAO,CAACX,QAAQ,GAAG+C,yBAAY,CAACT,GAAG,EAAE,IAAI,CAAC3B,OAAO,CAAChB,SAAS,EAAE,IAAI,CAACgB,OAAO,CAACV,UAAU,CAAC,GAAGqC,GAAG,CAAA;EACzG,GAAA;IAEArB,wBAAwBA,CAACqB,GAAG,EAAE;EAC5B,IAAA,OAAOU,gBAAO,CAACV,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAA;EAC7B,GAAA;EAEAM,EAAAA,qBAAqBA,CAACK,OAAO,EAAEV,eAAe,EAAE;EAC9C,IAAA,IAAI,IAAI,CAAC5B,OAAO,CAACZ,IAAI,EAAE;QACrBwC,eAAe,CAACX,SAAS,GAAG,EAAE,CAAA;EAC9BW,MAAAA,eAAe,CAACW,MAAM,CAACD,OAAO,CAAC,CAAA;EAC/B,MAAA,OAAA;EACF,KAAA;EAEAV,IAAAA,eAAe,CAACO,WAAW,GAAGG,OAAO,CAACH,WAAW,CAAA;EACnD,GAAA;EACF;;;;;;;;"}