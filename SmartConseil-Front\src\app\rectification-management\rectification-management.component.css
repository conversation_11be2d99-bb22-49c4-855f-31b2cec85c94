.badge-warning {
  background-color: #ffc107;
  color: #000;
}

.badge-info {
  background-color: #0dcaf0;
  color: #000;
}

.badge-success {
  background-color: #198754;
  color: #fff;
}

.badge-danger {
  background-color: #dc3545;
  color: #fff;
}

.badge-secondary {
  background-color: #6c757d;
  color: #fff;
}

.text-success {
  color: #198754 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.text-muted {
  color: #6c757d !important;
}

.modal.show {
  background-color: rgba(0, 0, 0, 0.5);
}

.table th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.075);
}

.card-header {
  background-color: #e9ecef;
  border-bottom: 1px solid #dee2e6;
}

.btn-group .btn {
  margin-right: 0;
}

.btn-success {
  background-color: #198754;
  border-color: #198754;
}

.btn-success:hover {
  background-color: #157347;
  border-color: #146c43;
}

.btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
}

.btn-danger:hover {
  background-color: #bb2d3b;
  border-color: #b02a37;
}

.alert {
  border-radius: 0.375rem;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

.fs-4 {
  font-size: 1.5rem;
}

.fa-3x {
  font-size: 3em;
}

.fa-2x {
  font-size: 2em;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.welcome-header {
  background: linear-gradient(180deg, #c51126 0%, #d6555c 50%, #992020 100%);
  color: white;
  padding: 2rem;
  border-radius: 10px;
  margin-bottom: 2rem;
  text-align: center;
}