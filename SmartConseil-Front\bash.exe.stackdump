Stack trace:
Frame         Function      Args
0007FFFFBBA0  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFBBA0, 0007FFFFAAA0) msys-2.0.dll+0x1FEBA
0007FFFFBBA0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE78) msys-2.0.dll+0x67F9
0007FFFFBBA0  000210046832 (000210285FF9, 0007FFFFBA58, 0007FFFFBBA0, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBA0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFBBA0  0002100690B4 (0007FFFFBBB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBE80  00021006A49D (0007FFFFBBB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC072D0000 ntdll.dll
7FFC07140000 KERNEL32.DLL
7FFC04F10000 KERNELBASE.dll
7FFC05D00000 USER32.dll
000210040000 msys-2.0.dll
7FFC04A10000 win32u.dll
7FFC05F30000 GDI32.dll
7FFC04A40000 gdi32full.dll
7FFC04B90000 msvcp_win.dll
7FFC04E10000 ucrtbase.dll
7FFC06050000 advapi32.dll
7FFC066B0000 msvcrt.dll
7FFC05A60000 sechost.dll
7FFC05BD0000 RPCRT4.dll
7FFC04B60000 bcrypt.dll
7FFC042E0000 CRYPTBASE.DLL
7FFC05260000 bcryptPrimitives.dll
7FFC05EA0000 IMM32.DLL
