.welcome-header {
  background: linear-gradient(to right, #2563eb, #1e40af); /* bleu dégradé */
  color: white;
  padding: 30px 40px;
  border-radius: 12px;
  margin-bottom: 20px;
}

.welcome-header h1 {
  font-size: 28px;
  font-weight: bold;
  margin: 0 0 8px 0;
  color: white;
}

.welcome-header p {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}
.stats-container {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  padding: 20px;
  gap: 20px;
}

.stat-card {
  background-color: #ffffff;
  border-radius: 15px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
  padding: 20px 30px;
  text-align: center;
  flex: 1 1 200px;
  max-width: 250px;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-label {
  font-size: 14px;
  color: #000000; /* gris foncé */
  margin-bottom: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #111827; /* noir bleuté */
}
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f9fafb; /* fond très clair */
  padding: 20px 30px;
  border-radius: 10px;
  margin-bottom: 20px;
}

.header-text h2 {
  margin: 0;
  font-size: 28px;
  color: #111827; /* noir bleuté */
  font-weight: 700;
}

.header-text p {
  margin: 5px 0 0 0;
  color: #6b7280; /* gris foncé */
  font-size: 14px;
}

.add-user-btn {
  background-color: #111827; /* bouton noir bleuté */
  color: white;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
}

.add-user-btn:hover {
  background-color: #1f2937;
}

.add-user-btn .icon {
  margin-right: 8px;
  font-size: 16px;
}
/* Modal background */
.modal {
  display: none; /* caché par défaut */
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0,0,0,0.4); /* fond semi-transparent */
}

/* Modal box */
.modal-content {
  background-color: #fff;
  margin: 10% auto;
  padding: 20px;
  border-radius: 10px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
  position: relative;
  animation: fadeIn 0.3s ease;
}

/* Animation */
@keyframes fadeIn {
  from {opacity: 0;}
  to {opacity: 1;}
}

/* Close button */
.close {
  color: #aaa;
  position: absolute;
  top: 12px;
  right: 16px;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close:hover {
  color: #000;
}

/* Form styles */
.modal-content input {
  width: 100%;
  margin: 10px 0;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #ccc;
}

.modal-content button[type="submit"] {
  background-color: #111827;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 6px;
  cursor: pointer;
}

.modal-content button[type="submit"]:hover {
  background-color: #1f2937;
}
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  justify-content: center;
  align-items: center;
}

.modal.show {
  display: flex;
}

.modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  width: 400px;
  max-width: 90%;
  position: relative;
}

.close {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 24px;
  cursor: pointer;
}
.stats-container {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  padding: 20px;
  gap: 20px;

  /* ↓↓↓ ajoute cette ligne ↓↓↓ */
  margin-top: -30px; /* ajuste à ta convenance */
}
select {
  width: 100%;
  max-width: 400px; /* adapte selon besoin */
  padding: 10px 15px;
  border: 1.5px solid #ccc;
  border-radius: 6px;
  background-color: #fff;
  font-size: 1rem;
  color: #333;
  appearance: none; /* enlève style par défaut navigateur */
  cursor: pointer;
  transition: border-color 0.3s ease;
}

/* Ajouter une petite flèche personnalisée */
select {
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg width='14' height='8' viewBox='0 0 14 8' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%23333' d='M7 8L0 0h14L7 8z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 15px center;
  background-size: 14px 8px;
  margin-bottom: 20px; /* espace sous chaque select */
}

/* Au focus, changer la bordure */
select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 5px rgba(0,123,255,0.5);
}

/* Survol */
select:hover {
  border-color: #888;
}
body {
  font-family: 'Segoe UI', sans-serif;
  background-color: #f8f9fb;
  margin: 0;
  padding: 30px;
}

.container {
  display: flex;
  gap: 30px;
  justify-content: center;
  align-items: flex-start;
}

.calendar, .actions {
  background-color: white;
  border-radius: 10px;
  padding: 20px 25px;
  width: 700px;
  box-shadow: 0 0 10px rgba(0,0,0,0.05);
}

.calendar h2, .actions h2 {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 5px;
}

.week, .subtitle {
  color: #7a7a7a;
  font-size: 14px;
  margin-bottom: 20px;
}

.days {
  display: flex;
  justify-content: space-between;
  margin-bottom: 25px;
}

.days div {
  text-align: center;
  color: #555;
  font-weight: 500;
}

.days div span {
  display: inline-block;
  margin-top: 5px;
  background-color: #f0f0f0;
  padding: 8px 12px;
  border-radius: 50%;
}

.days .active span {
  background-color: #0053f9;
  color: white;
}

.event {
  background-color: #eaf2ff;
  padding: 12px 16px;
  border-radius: 10px;
  margin-bottom: 15px;
  position: relative;
  height: 70px;
}

.event p {
  margin: 5px 0 0;
  color: #333;
}

.status {
  position: absolute;
  right: 15px;
  top: 15px;
  padding: 3px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status.confirmed {
  background-color: #d4f5dd;
  color: #118b2e;
}

.status.pending {
  background-color: #fff3cd;
  color: #856404;
}

.actions button {
  display: block;
  width: 100%;
  margin-bottom: 15px;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  padding: 12px;
  text-align: left;
  font-size: 15px;
  font-weight: 500;
  border-radius: 10px;
  cursor: pointer;
  transition: 0.2s;
}

.actions button:hover {
  background-color: #eaf0ff;
}
.actions {
  background-color: white;
  border-radius: 10px;
  padding: 20px 25px;
  width: 500px;
  box-shadow: 0 0 10px rgba(0,0,0,0.05);
  min-height: 250px; /* ✅ Ajoute cette ligne */
}

/* Participants Assignment Modal Styles */
.participants-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.participants-modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.participants-modal-content {
  background-color: white;
  padding: 30px;
  border-radius: 15px;
  width: 95%;
  max-width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease;
}

.participants-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f3f4f6;
}

.participants-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 24px;
  font-weight: 600;
}

.participants-actions {
  display: flex;
  gap: 15px;
  margin-bottom: 25px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 10px;
}

.action-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.save-btn {
  background: linear-gradient(135deg, #0051ff 0%, #003cff 100%);
  color: white;
}

.save-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(37, 99, 235, 0.4);
}

.clear-btn {
 background: linear-gradient(135deg, #0051ff 0%, #003cff 100%);
  color: white;
}

.clear-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(220, 38, 38, 0.4);
}

.participants-content {
  display: block;
}

.users-section-full {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.users-section-full h4 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
  padding-bottom: 10px;
  border-bottom: 2px solid #2563eb;
}

.users-list {
  max-height: 600px;
  overflow-y: auto;
}

.user-card {
  background: white;
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 10px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.user-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.user-info {
  margin-bottom: 15px;
}

.user-info .user-name {
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.user-email {
  color: #6b7280;
  font-size: 14px;
}

.user-role {
  color: #2563eb;
  font-size: 12px;
  font-weight: 500;
  background: #eff6ff;
  padding: 2px 6px;
  border-radius: 4px;
  width: fit-content;
}

.assignment-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 10px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.checkbox-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #2563eb;
  cursor: pointer;
}

.checkbox-label {
  font-size: 12px;
  color: #374151;
  cursor: pointer;
  font-weight: 500;
}

.checkbox-label:hover {
  color: #2563eb;
}

/* Responsive Participants Modal */
@media (max-width: 768px) {
  .participants-modal-content {
    padding: 20px;
    width: 98%;
  }

  .participants-actions {
    flex-direction: column;
  }

  .assignment-checkboxes {
    grid-template-columns: 1fr;
  }
}

/* Calendar Modal Styles */
.calendar-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.calendar-modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.calendar-modal-content {
  background-color: white;
  padding: 30px;
  border-radius: 15px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f3f4f6;
}

.calendar-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 24px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 30px;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.calendar-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 20px;
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  border-radius: 10px;
  color: white;
}

.calendar-navigation h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.nav-btn, .today-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.nav-btn:hover, .today-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.today-btn {
  font-size: 14px;
  padding: 8px 16px;
}

.calendar-grid {
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.weekday {
  padding: 15px 10px;
  text-align: center;
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.calendar-day {
  min-height: 80px;
  padding: 8px;
  border: 1px solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  flex-direction: column;
}

.calendar-day:hover {
  background-color: #f8f9fa;
}

.calendar-day.other-month {
  color: #adb5bd;
  background-color: #f8f9fa;
}

.calendar-day.today {
  background-color: #e3f2fd;
  border-color: #2196f3;
}

.calendar-day.today .day-number {
  background-color: #008cff;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.calendar-day.has-conseil {
  background-color: #fff3cd;
  border-color: #ffc107;
}

.calendar-day.selected {
  background-color: #d4edda;
  border-color: #28a745;
  border-width: 2px;
}

.day-number {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
}

.conseil-indicators {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.conseil-indicator {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.conseil-indicator:hover {
  transform: scale(1.05);
}

.calendar-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #495057;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

.today-color {
  background-color: #2196f3;
}

.conseil-color {
  background-color: #ffc107;
}

.selected-color {
  background-color: #28a745;
}

/* Responsive Calendar */
@media (max-width: 768px) {
  .calendar-modal-content {
    padding: 20px;
    width: 95%;
  }

  .calendar-navigation {
    flex-direction: column;
    gap: 10px;
  }

  .calendar-navigation h4 {
    order: -1;
  }

  .calendar-day {
    min-height: 60px;
    padding: 4px;
  }

  .conseil-indicator {
    font-size: 9px;
    padding: 1px 4px;
  }

  .calendar-legend {
    flex-direction: column;
    gap: 10px;
  }
}

/* Content Centering and Layout Fixes */
:host {
  display: block;
  width: 100%;
}

.enseignant-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  width: 100%;
}

/* Ensure proper alignment */
.container-fluid {
  text-align: left;
  margin: 0 auto;
  width: 100%;
  max-width: none;
}

/* Fix any right alignment issues */
* {
  box-sizing: border-box;
}

/* Remove any unwanted margins or padding that could cause right alignment */
.body-wrapper,
.body-wrapper-inner {
  width: 100%;
  margin: 0;
  padding: 0;
  text-align: left;
}

/* Ensure all main containers are properly centered */
.welcome-header,
.header-container,
.stats-container {
  margin-left: auto;
  margin-right: auto;
  text-align: left;
}

/* Additional responsive fixes */
@media (max-width: 768px) {
  .enseignant-content {
    padding: 0 0.5rem;
  }

  .welcome-header,
  .header-container {
    margin: 0 auto 2rem auto;
  }
}

