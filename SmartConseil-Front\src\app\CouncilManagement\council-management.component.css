.welcome-header {
  background: linear-gradient(to right, #2563eb, #1e40af);
  color: white;
  padding: 30px 40px;
  border-radius: 12px;
  margin-bottom: 20px;
}

.welcome-header h1 {
  font-size: 28px;
  font-weight: bold;
  margin: 0 0 8px 0;
  color: white;
}

.welcome-header p {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px 30px;
  border-radius: 10px;
  margin-bottom: 30px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.header-text h2 {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 5px 0;
  color: #1f2937;
}

.header-text p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.add-user-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.add-user-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.add-user-btn .icon {
  font-size: 16px;
}

body {
  font-family: 'Segoe UI', sans-serif;
  background-color: #f8f9fb;
  margin: 0;
  padding: 30px;
}

.container {
  display: flex;
  gap: 30px;
  justify-content: center;
  align-items: flex-start;
}

.calendar, .actions {
  background-color: white;
  border-radius: 10px;
  padding: 20px 25px;
  width: 700px;
  box-shadow: 0 0 10px rgba(0,0,0,0.05);
}

.calendar h2, .actions h2 {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 5px;
}

.week, .subtitle {
  color: #7a7a7a;
  font-size: 14px;
  margin-bottom: 20px;
}

.event {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  position: relative;
  transition: all 0.2s ease;
}

.event:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.event.confirmed {
  border-left: 4px solid #28a745;
  background-color: #f8fff9;
}

.event.pending {
  border-left: 4px solid #ffc107;
  background-color: #fffdf5;
}

.event.closed {
  border-left: 4px solid #dc3545;
  background-color: #fff5f5;
}

.actions button {
  display: block;
  width: 100%;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  padding: 12px 15px;
  margin-bottom: 10px;
  border-radius: 6px;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.actions button:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
  transform: translateX(5px);
}

.big-card {
  background-color: white;
  border-radius: 12px;
  padding: 30px;
  margin-top: 30px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.big-card h2 {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #1f2937;
}

.subtext {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 25px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin: 25px 0;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 25px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.card {
  background: #fff;
  margin: 20px 0;
  padding: 25px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.header {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f3f4f6;
}

.header span {
  margin-left: 10px;
}

.status {
  margin-left: auto;
  padding: 6px 14px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status.active {
  background-color: #d1fae5;
  color: #065f46;
}

.status.closed {
  background-color: #fee2e2;
  color: #991b1b;
}

.status.pending {
  background-color: #fef3c7;
  color: #92400e;
}

.top-students {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 20px;
  border-radius: 10px;
  margin: 15px 0;
  border-left: 4px solid #3b82f6;
}

.top-students h4 {
  margin: 0 0 15px 0;
  color: #1e40af;
  font-size: 16px;
  font-weight: 600;
}

.student-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e2e8f0;
  transition: background-color 0.2s ease;
}

.student-item:hover {
  background-color: rgba(59, 130, 246, 0.05);
  border-radius: 6px;
  padding-left: 10px;
  padding-right: 10px;
}

.student-item:last-child {
  border-bottom: none;
}

.vote-section {
  background: linear-gradient(135deg, #fef7ff 0%, #f3e8ff 100%);
  padding: 20px;
  border-radius: 10px;
  margin: 15px 0;
  border-left: 4px solid #8b5cf6;
}

.vote-section h4 {
  margin: 0 0 15px 0;
  color: #7c3aed;
  font-size: 16px;
  font-weight: 600;
}

.vote-buttons {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 18px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2563eb;
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #4b5563;
  transform: translateY(-1px);
}

.btn-success {
  background-color: #10b981;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background-color: #059669;
  transform: translateY(-1px);
}

.btn-warning {
  background-color: #f59e0b;
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background-color: #d97706;
  transform: translateY(-1px);
}

.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  backdrop-filter: blur(4px);
}

.modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  background-color: white;
  padding: 35px;
  border-radius: 15px;
  width: 450px;
  max-width: 90%;
  box-shadow: 0 20px 60px rgba(0,0,0,0.3);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.modal-content h3 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
}

.form-group input, 
.form-group select, 
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.form-group input:focus, 
.form-group select:focus, 
.form-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
    gap: 20px;
  }
  
  .calendar, .actions {
    width: 100%;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
  
  .header-container {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
}
