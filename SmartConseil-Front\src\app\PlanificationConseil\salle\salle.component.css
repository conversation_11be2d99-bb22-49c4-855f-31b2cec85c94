.welcome-header {
  background: linear-gradient(to right, #2563eb, #1e40af); /* bleu dégradé */
  color: white;
  padding: 30px 40px;
  border-radius: 12px;
  margin-bottom: 20px;
}

.welcome-header h1 {
  font-size: 28px;
  font-weight: bold;
  margin: 0 0 8px 0;
  color: white;
}

.welcome-header p {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}
.stats-container {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  padding: 20px;
  gap: 20px;
}

.stat-card {
  background-color: #ffffff;
  border-radius: 15px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
  padding: 20px 30px;
  text-align: center;
  flex: 1 1 200px;
  max-width: 250px;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-label {
  font-size: 14px;
  color: #000000; /* gris foncé */
  margin-bottom: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #111827; /* noir bleuté */
}
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f9fafb; /* fond très clair */
  padding: 20px 30px;
  border-radius: 10px;
  margin-bottom: 20px;
}

.header-text h2 {
  margin: 0;
  font-size: 28px;
  color: #111827; /* noir bleuté */
  font-weight: 700;
}

.header-text p {
  margin: 5px 0 0 0;
  color: #6b7280; /* gris foncé */
  font-size: 14px;
}

.add-user-btn {
  background-color: #111827; /* bouton noir bleuté */
  color: white;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
}

.add-user-btn:hover {
  background-color: #1f2937;
}

.add-user-btn .icon {
  margin-right: 8px;
  font-size: 16px;
}
/* Modal background */
.modal {
  display: none; /* caché par défaut */
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0,0,0,0.4); /* fond semi-transparent */
}

/* Modal box */
.modal-content {
  background-color: #fff;
  margin: 10% auto;
  padding: 20px;
  border-radius: 10px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
  position: relative;
  animation: fadeIn 0.3s ease;
}

/* Animation */
@keyframes fadeIn {
  from {opacity: 0;}
  to {opacity: 1;}
}

/* Close button */
.close {
  color: #aaa;
  position: absolute;
  top: 12px;
  right: 16px;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close:hover {
  color: #000;
}

/* Form styles */
.modal-content input {
  width: 100%;
  margin: 10px 0;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #ccc;
}

.modal-content button[type="submit"] {
  background-color: #111827;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 6px;
  cursor: pointer;
}

.modal-content button[type="submit"]:hover {
  background-color: #1f2937;
}
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  justify-content: center;
  align-items: center;
}

.modal.show {
  display: flex;
}

.modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  width: 400px;
  max-width: 90%;
  position: relative;
}

.close {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 24px;
  cursor: pointer;
}
.stats-container {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  padding: 20px;
  gap: 20px;

  /* ↓↓↓ ajoute cette ligne ↓↓↓ */
  margin-top: -30px; /* ajuste à ta convenance */
}
select {
  width: 100%;
  max-width: 400px; /* adapte selon besoin */
  padding: 10px 15px;
  border: 1.5px solid #ccc;
  border-radius: 6px;
  background-color: #fff;
  font-size: 1rem;
  color: #333;
  appearance: none; /* enlève style par défaut navigateur */
  cursor: pointer;
  transition: border-color 0.3s ease;
}

/* Ajouter une petite flèche personnalisée */
select {
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg width='14' height='8' viewBox='0 0 14 8' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%23333' d='M7 8L0 0h14L7 8z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 15px center;
  background-size: 14px 8px;
  margin-bottom: 20px; /* espace sous chaque select */
}

/* Au focus, changer la bordure */
select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 5px rgba(0,123,255,0.5);
}

/* Survol */
select:hover {
  border-color: #888;
}
body {
  font-family: 'Segoe UI', sans-serif;
  background-color: #f8f9fb;
  margin: 0;
  padding: 40px;
}

.form-container {
  background-color: white;
  border-radius: 12px;
  padding: 30px;
  max-width: 1100px;
  margin: 0 auto;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.05);
}

h2 {
  margin: 0;
  font-size: 22px;
}

.subtitle {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 30px;
}

form {
  width: 100%;
}

.row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.field {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.field.full {
  flex: 100%;
}

label {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  margin-bottom: 8px;
}

input, select, textarea {
  padding: 10px 12px;
  font-size: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
}

textarea {
  resize: vertical;
  height: 100px;
}

.actions {
  margin-top: 30px;
  display: flex;
  gap: 15px;
}

button {
  padding: 10px 20px;
  font-size: 15px;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  border: none;
}

button.primary {
  background-color: #0d1117;
  color: white;
  display: flex;
  align-items: center;
  gap: 6px;
}

button.secondary {
  background-color: #f1f1f1;
  color: #000;
}

  body {
    font-family: 'Segoe UI', sans-serif;
    background-color: #f6f9fc;
    margin: 0;
    padding: 0;
  }

  .form-container {
    background-color: #ffffff;
    border-radius: 16px;
    padding: 40px;
    margin: 40px auto;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    max-width: 1100px;
  }

  .form-container h2 {
    font-size: 24px;
    margin-bottom: 10px;
    color: #2f3542;
  }

  .subtitle {
    color: #57606f;
    margin-bottom: 30px;
    font-size: 14px;
  }

  .row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }

  .field {
    flex: 1 1 45%;
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;
  }

  .field label {
    font-weight: 600;
    margin-bottom: 6px;
    color: #2f3542;
  }

  .field input {
    padding: 12px 15px;
    border: 1px solid #ced6e0;
    border-radius: 10px;
    font-size: 14px;
    transition: all 0.3s ease;
  }

  .field input:focus {
    border-color: #3b82f6;
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }

  .actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
  }

  .actions button {
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    border: none;
    cursor: pointer;
    transition: background 0.3s ease;
  }

  .actions .primary {
    background-color: #3b82f6;
    color: white;
  }

  .actions .primary:hover {
    background-color: #2563eb;
  }

  .actions .secondary {
    background-color: #e0e0e0;
    color: #333;
  }

  .actions .secondary:hover {
    background-color: #c7c7c7;
  }

  @media (max-width: 768px) {
    .field {
      flex: 1 1 100%;
    }
    .form-container {
      padding: 20px;
    }
  }
  .field select {
  padding: 12px 15px;
  border: 1px solid #ced6e0;
  border-radius: 10px;
  font-size: 14px;
  background-color: white;
  transition: all 0.3s ease;
}

.field select:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}
.error {
  color: #e53935; /* rouge vif */
  font-size: 13px;
  margin-top: 4px;
  font-weight: 500;
}
input.ng-invalid.ng-touched,
select.ng-invalid.ng-touched {
  border-color: #e53935;
  box-shadow: 0 0 0 1px rgba(229, 57, 53, 0.3);
}

