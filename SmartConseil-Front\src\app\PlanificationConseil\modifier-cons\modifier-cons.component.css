/* Styles pour le composant de modification - même design que les autres pages */

.welcome-header {
  background: linear-gradient(to right, #2563eb, #1e40af); /* bleu dégradé */
  color: white;
  padding: 30px 40px;
  border-radius: 12px;
  margin-bottom: 20px;
}

.welcome-header h1 {
  font-size: 28px;
  font-weight: bold;
  margin: 0 0 8px 0;
  color: white;
}

.welcome-header p {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f9fafb; /* fond très clair */
  padding: 20px 30px;
  border-radius: 10px;
  margin-bottom: 20px;
}

.header-text h2 {
  margin: 0;
  font-size: 28px;
  color: #111827; /* noir bleuté */
  font-weight: 700;
}

.header-text p {
  margin: 5px 0 0 0;
  color: #6b7280; /* gris foncé */
  font-size: 14px;
}

.add-user-btn {
  background-color: #111827; /* bouton noir bleuté */
  color: white;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
}

.add-user-btn:hover {
  background-color: #1f2937;
}

.add-user-btn .icon {
  margin-right: 8px;
  font-size: 16px;
}

.form-container {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-container h2 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 1.8rem;
}

.subtitle {
  color: #6c757d;
  margin-bottom: 30px;
  font-size: 1rem;
}

.row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.field {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.field.full {
  flex: 100%;
}

.field label {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 0.95rem;
}

.field input,
.field select,
.field textarea {
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

.field input:focus,
.field select:focus,
.field textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.field textarea {
  min-height: 100px;
  resize: vertical;
}

.error {
  color: #dc3545;
  font-size: 0.85rem;
  margin-top: 5px;
  font-weight: 500;
}

.actions {
  display: flex;
  gap: 15px;
  margin-top: 30px;
  justify-content: center;
}

.primary,
.secondary {
  padding: 12px 30px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.primary {
  background: #28a745;
  color: white;
  border: none;
}

.primary:hover:not(:disabled) {
  background: #218838;
  transform: translateY(-2px);
}

.primary:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.secondary {
  background: #6c757d;
  color: white;
  border: none;
}

.secondary:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

/* Responsive design */
@media (max-width: 768px) {
  .row {
    flex-direction: column;
    gap: 15px;
  }
  
  .header-container {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .primary,
  .secondary {
    width: 100%;
    justify-content: center;
  }
}
