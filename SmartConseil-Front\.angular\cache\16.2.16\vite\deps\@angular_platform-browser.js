import {
  BrowserDom<PERSON><PERSON>pter,
  BrowserGetTestability,
  BrowserModule,
  By,
  DomEventsPlugin,
  DomRendererFactory2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>mp<PERSON>,
  EVENT_MANAGER_PLUGINS,
  EventManager,
  HAMMER_GESTURE_CONFIG,
  HAMMER_LOADER,
  HammerGestureConfig,
  HammerGesturesPlugin,
  HammerModule,
  INTERNAL_BROWSER_PLATFORM_PROVIDERS,
  KeyEventsPlugin,
  Meta,
  REMOVE_STYLES_ON_COMPONENT_DESTROY,
  SharedStylesHost,
  Title,
  TransferState,
  VERSION,
  bootstrapApplication,
  createApplication,
  disableDebugTools,
  enableDebugTools,
  initDomAdapter,
  makeStateKey,
  platformBrowser,
  provideClientHydration,
  provideProtractorTestingSupport,
  withNoDomReuse,
  withNoHttpTransferCache
} from "./chunk-5D7HY3RR.js";
import "./chunk-FQDSQOUL.js";
import {
  getDOM
} from "./chunk-LGXTCWBQ.js";
import "./chunk-PILRD47O.js";
import "./chunk-Y5I2RZQE.js";
import "./chunk-J6FGIEE5.js";
import "./chunk-OIMGJFGN.js";
import "./chunk-W44PFRPX.js";
import "./chunk-KMNONVFS.js";
export {
  BrowserModule,
  By,
  DomSanitizer,
  EVENT_MANAGER_PLUGINS,
  EventManager,
  HAMMER_GESTURE_CONFIG,
  HAMMER_LOADER,
  HammerGestureConfig,
  HammerModule,
  Meta,
  REMOVE_STYLES_ON_COMPONENT_DESTROY,
  Title,
  TransferState,
  VERSION,
  bootstrapApplication,
  createApplication,
  disableDebugTools,
  enableDebugTools,
  makeStateKey,
  platformBrowser,
  provideClientHydration,
  provideProtractorTestingSupport,
  withNoDomReuse,
  withNoHttpTransferCache,
  BrowserDomAdapter as ɵBrowserDomAdapter,
  BrowserGetTestability as ɵBrowserGetTestability,
  DomEventsPlugin as ɵDomEventsPlugin,
  DomRendererFactory2 as ɵDomRendererFactory2,
  DomSanitizerImpl as ɵDomSanitizerImpl,
  HammerGesturesPlugin as ɵHammerGesturesPlugin,
  INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS,
  KeyEventsPlugin as ɵKeyEventsPlugin,
  SharedStylesHost as ɵSharedStylesHost,
  getDOM as ɵgetDOM,
  initDomAdapter as ɵinitDomAdapter
};
//# sourceMappingURL=@angular_platform-browser.js.map
