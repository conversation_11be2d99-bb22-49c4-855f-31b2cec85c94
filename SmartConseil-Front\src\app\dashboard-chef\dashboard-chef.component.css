.welcome-header {
  background: linear-gradient(180deg, #c51126 0%, #d6555c 50%, #992020 100%);
  color: white;
  padding: 2rem;
  border-radius: 10px;
  margin-bottom: 2rem;
  text-align: center;
}

.welcome-header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.welcome-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.card {
  border: none;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  transition: transform 0.3s ease;
  cursor: pointer;
}

.card:hover {
  transform: translateY(-5px);
}

.sidebar-link {
  cursor: pointer;
  transition: all 0.3s ease;
}

.sidebar-link:hover {
  background-color: #f8f9fa;
  border-radius: 5px;
}

.round-20 {
  width: 20px;
  height: 20px;
}

.bg-light-primary {
  background-color: rgba(13, 110, 253, 0.1);
}

.bg-light-success {
  background-color: rgba(25, 135, 84, 0.1);
}

.bg-light-warning {
  background-color: rgba(255, 193, 7, 0.1);
}

.bg-light-info {
  background-color: rgba(13, 202, 240, 0.1);
}

.modal {
  background-color: rgba(0, 0, 0, 0.5);
}

.modal.show {
  display: block !important;
}

.table th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.badge {
  font-size: 0.75rem;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}
