<div class="reset-password-container" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255,255,255,0.95); z-index: 1000; display: flex; flex-direction: column; align-items: center; justify-content: center;">
  
    <!-- Close Button -->
    <button style="position: absolute; top: 20px; right: 20px; background: none; border: none; font-size: 24px; cursor: pointer;">
      ✕
    </button>
    
    <!-- Form Content -->
    <div style="width: 90%; max-width: 400px; text-align: center;">
      <h2 style="color: #dd9425; font-size: 28px; margin-bottom: 30px;">Reset Your Password</h2>
      
      <div style="background: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
        <div style="margin-bottom: 20px; text-align: left;">
          <label style="display: block; margin-bottom: 8px; font-weight: 500;">New Password</label>
          <input type="password" 
                 [(ngModel)]="newPassword" 
                 placeholder="Enter new password"
                 style="width: 100%; padding: 12px 15px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px;">
        </div>
        
        <div style="margin-bottom: 15px; text-align: left;">
          <label style="display: block; margin-bottom: 8px; font-weight: 500;">Confirm Password</label>
          <input type="password" 
                 [(ngModel)]="confirmPassword" 
                 placeholder="Confirm new password"
                 style="width: 100%; padding: 12px 15px; border: 2px solid {{ newPassword && confirmPassword && newPassword !== confirmPassword ? 'red' : '#ddd' }}; border-radius: 8px; font-size: 16px;">
        </div>

        <!-- Password mismatch message (pure template logic) -->
        <div *ngIf="newPassword && confirmPassword && newPassword !== confirmPassword" 
             style="color: red; margin: -10px 0 15px 0; font-size: 14px; text-align: left;">
          ✗ Passwords do not match
        </div>
        
        <button type="button" (click)="onSubmit()"
                [disabled]="!newPassword || !confirmPassword || newPassword !== confirmPassword"
                style="width: 100%; background: {{ !newPassword || !confirmPassword || newPassword !== confirmPassword ? '#cccccc' : '#b7d33b' }}; color: white; border: none; padding: 12px; border-radius: 8px; font-size: 16px; cursor: {{ !newPassword || !confirmPassword || newPassword !== confirmPassword ? 'not-allowed' : 'pointer' }};">
         Reset Password
        </button>
        
        <div *ngIf="message" 
             style="margin-top: 15px; padding: 10px; border-radius: 5px; 
                    background: {{ message.includes('Error') ? '#ffebee' : '#e8f5e9' }};
                    color: {{ message.includes('Error') ? '#c62828' : '#2e7d32' }};">
          {{ message }}
        </div>
        
        <p style="margin-top: 20px; color: #666;">
          Remember your password? 
          <a  style="color: #b7d33b; cursor: pointer;">Sign in</a>
        </p>
      </div>
    </div>
</div>