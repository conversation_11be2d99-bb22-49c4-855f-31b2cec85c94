/* Clean profile picture container */
.profile-picture-container {
  position: relative;
  display: inline-block;
  margin: 0;
  padding: 0;
  line-height: 0;
  background: transparent;
}

/* Profile picture image - base styling */
.profile-picture-img {
  object-fit: cover;
  transition: all 0.3s ease;
  border-radius: 50%;
  display: block;
  margin: 0;
  padding: 0;
  background: transparent;
}

/* Shape classes */
.profile-picture-circle {
  border-radius: 50% !important;
}

.profile-picture-square {
  border-radius: 8px !important;
}

/* Default border styling */
.profile-picture-border {
  border: 2px solid #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Loading state */
.profile-picture-loading {
  background-color: #f8f9fa;
  border: 2px solid #dee2e6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Size variations */
.profile-picture-small {
  width: 32px;
  height: 32px;
}

.profile-picture-small .profile-picture-img,
.profile-picture-small .profile-picture-loading {
  width: 32px;
  height: 32px;
}

.profile-picture-medium {
  width: 42px;
  height: 42px;
}

.profile-picture-medium .profile-picture-img,
.profile-picture-medium .profile-picture-loading {
  width: 42px;
  height: 42px;
}

.profile-picture-large {
  width: 80px;
  height: 80px;
}

.profile-picture-large .profile-picture-img,
.profile-picture-large .profile-picture-loading {
  width: 80px;
  height: 80px;
}

/* Hover effects */
.profile-picture-img:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Loading spinner positioning */
.profile-picture-loading.profile-picture-small {
  width: 32px;
  height: 32px;
}

.profile-picture-loading.profile-picture-medium {
  width: 42px;
  height: 42px;
}

.profile-picture-loading.profile-picture-large {
  width: 80px;
  height: 80px;
}

.profile-picture-loading .spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .profile-picture-large .profile-picture-img,
  .profile-picture-large .profile-picture-loading {
    width: 60px;
    height: 60px;
  }
}
