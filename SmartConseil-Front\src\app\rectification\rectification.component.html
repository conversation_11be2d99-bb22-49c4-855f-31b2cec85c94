<app-layout>
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h4 class="mb-0">
            <i class="ti ti-edit me-2"></i>
            Demande de Rectification
          </h4>
        </div>
        <div class="card-body">
          <form (ngSubmit)="submit()" #rectificationForm="ngForm">
            <div class="row">
              <!-- Student Information -->
              <div class="col-md-6 mb-3">
                <label for="etudiantPrenom" class="form-label">Prénom de l'étudiant</label>
                <input type="text" class="form-control" id="etudiantPrenom" name="etudiantPrenom"
                       [(ngModel)]="formData.etudiantPrenom" placeholder="Prénom de l'étudiant" required>
              </div>
              <div class="col-md-6 mb-3">
                <label for="etudiantNom" class="form-label">Nom de l'étudiant</label>
                <input type="text" class="form-control" id="etudiantNom" name="etudiantNom"
                       [(ngModel)]="formData.etudiantNom" placeholder="Nom de l'étudiant" required>
              </div>

              <!-- Academic Information -->
              <div class="col-md-4 mb-3">
                <label for="secteur" class="form-label">Secteur</label>
                <select class="form-select" id="secteur" name="secteur" [(ngModel)]="selectedSecteur"
                        (change)="onSecteurChange()" required>
                  <option value="">Sélectionner un secteur</option>
                  <option *ngFor="let secteur of secteurs" [value]="secteur">{{ secteur }}</option>
                </select>
              </div>
              <div class="col-md-4 mb-3">
                <label for="option" class="form-label">Option</label>
                <select class="form-select" id="option" name="option" [(ngModel)]="selectedOption"
                        (change)="onOptionChange()" required [disabled]="!selectedSecteur">
                  <option value="">Sélectionner une option</option>
                  <option *ngFor="let option of options" [value]="option">{{ option }}</option>
                </select>
              </div>
              <div class="col-md-4 mb-3">
                <label for="classe" class="form-label">Classe</label>
                <select class="form-select" id="classe" name="classe" [(ngModel)]="formData.classe"
                        required [disabled]="!selectedOption">
                  <option value="">Sélectionner une classe</option>
                  <option *ngFor="let classe of classes" [value]="classe">{{ classe }}</option>
                </select>
              </div>

              <!-- Grade Information -->
              <div class="col-md-6 mb-3">
                <label for="ancienneNote" class="form-label">Ancienne Note</label>
                <input type="number" class="form-control" id="ancienneNote" name="ancienneNote"
                       [(ngModel)]="formData.ancienneNote" placeholder="Ancienne note"
                       min="0" max="20" step="0.01" required>
              </div>
              <div class="col-md-6 mb-3">
                <label for="nouvelleNote" class="form-label">Nouvelle Note</label>
                <input type="number" class="form-control" id="nouvelleNote" name="nouvelleNote"
                       [(ngModel)]="formData.nouvelleNote" placeholder="Nouvelle note"
                       min="0" max="20" step="0.01" required>
              </div>

              <!-- Justification -->
              <div class="col-12 mb-3">
                <label for="justification" class="form-label">Justification</label>
                <select class="form-select" id="justification" name="justification"
                        [(ngModel)]="formData.justification" required>
                  <option value="">Sélectionner une justification</option>
                  <option *ngFor="let justification of justifications" [value]="justification">
                    {{ justification }}
                  </option>
                </select>
              </div>
            </div>

            <div class="d-flex justify-content-end">
              <button type="button" class="btn btn-secondary me-2" (click)="resetForm()">
                <i class="ti ti-refresh me-1"></i>
                Réinitialiser
              </button>
              <button type="submit" class="btn btn-primary" [disabled]="!rectificationForm.form.valid">
                <i class="ti ti-send me-1"></i>
                Soumettre la Demande
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <div class="row mt-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h4 class="mb-0">
            <i class="ti ti-history me-2"></i>
            Historique des Rectifications
          </h4>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-striped table-hover">
              <thead class="table-dark">
                <tr>
                  <th>Étudiant</th>
                  <th>Classe</th>
                  <th>Option</th>
                  <th>Ancienne Note</th>
                  <th>Nouvelle Note</th>
                  <th>Justification</th>
                  <th>Statut</th>
                  <th>Date</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let r of rectifications">
                  <td>{{ r.etudiantPrenom }} {{ r.etudiantNom }}</td>
                  <td>{{ r.classe }}</td>
                  <td>{{ r.option }}</td>
                  <td>
                    <span class="badge bg-danger">{{ r.ancienneNote }}</span>
                  </td>
                  <td>
                    <span class="badge bg-success">{{ r.nouvelleNote }}</span>
                  </td>
                  <td>
                    <span class="text-truncate d-inline-block" style="max-width: 200px;"
                          [title]="r.justification">
                      {{ r.justification }}
                    </span>
                  </td>
                  <td>
                    <span class="badge"
                          [ngClass]="{
                            'bg-warning': r.status === 'EN_ATTENTE',
                            'bg-success': r.status === 'ACCEPTEE',
                            'bg-danger': r.status === 'REFUSEE'
                          }">
                      {{ r.status }}
                    </span>
                  </td>
                  <td>{{ r.dateDemande | date:'short' }}</td>
                </tr>
                <tr *ngIf="rectifications.length === 0">
                  <td colspan="8" class="text-center text-muted py-4">
                    <i class="ti ti-inbox fs-1 d-block mb-2"></i>
                    Aucune rectification trouvée
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</app-layout>
