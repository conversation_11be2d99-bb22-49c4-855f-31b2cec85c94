.session-container {
  height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #ffffff 100%);
  display: flex;
  flex-direction: column;
}

/* <PERSON><PERSON> de la session */
.session-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.session-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.session-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #000000;
  display: flex;
  align-items: center;
  gap: 12px;
}

.session-title i {
  color: #000000;
  font-size: 28px;
}

.session-details {
  display: flex;
  gap: 20px;
  font-size: 14px;
}

.conseil-classe {
  background: #000000;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-weight: 500;
}

.conseil-date {
  color: #333333;
  font-weight: 500;
}

.session-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  background: #f5f5f5;
  color: #000000;
  font-size: 14px;
  font-weight: 500;
}

.connection-status.connected {
  background: #e5e5e5;
  color: #000000;
}

.connection-status i {
  font-size: 12px;
}

.quit-btn {
  background: #000000;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quit-btn:hover {
  background: #333333;
  transform: translateY(-1px);
}

/* Contenu principal */
.session-content {
  flex: 1;
  display: flex;
  gap: 20px;
  padding: 20px 30px;
  overflow: hidden;
}

/* Panneau des participants */
.participants-panel {
  width: 350px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
  background: rgba(16, 185, 129, 0.05);
}

.panel-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 10px;
}

.panel-header i {
  color: #10b981;
}

.participants-list {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.participant-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 12px;
  border: 2px solid transparent;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.participant-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.participant-card.current-user {
  border-color: #10b981;
  background: linear-gradient(135deg, #ecfdf5, #f0fdf4);
}

.participant-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #10b981, #059669);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.participant-info {
  flex: 1;
}

.participant-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.you-badge {
  background: #10b981;
  color: white;
  font-size: 11px;
  padding: 2px 8px;
  border-radius: 10px;
  font-weight: 500;
}

.participant-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #6b7280;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.online {
  background: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.action-btn {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f3f4f6;
  color: #6b7280;
}

.no-participants {
  text-align: center;
  padding: 40px 20px;
  color: #9ca3af;
}

.no-participants i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

/* Zone de contenu principal */
.main-content {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.content-placeholder {
  text-align: center;
  color: #6b7280;
}

.content-placeholder i {
  font-size: 64px;
  margin-bottom: 20px;
  color: #d1d5db;
}

.content-placeholder h3 {
  font-size: 24px;
  margin-bottom: 12px;
  color: #374151;
}

.content-placeholder p {
  margin: 8px 0;
  font-size: 16px;
}
