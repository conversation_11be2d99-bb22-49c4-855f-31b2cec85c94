{"version": 3, "file": "simplebar.esm.js", "sources": ["../src/helpers.js", "../src/scrollbar-width.js", "../src/simplebar.js", "../src/index.js"], "sourcesContent": ["// Helper function to retrieve options from element attributes\nexport const getOptions = function(obj) {\n  const options = Array.prototype.reduce.call(\n    obj,\n    (acc, attribute) => {\n      const option = attribute.name.match(/data-simplebar-(.+)/);\n      if (option) {\n        const key = option[1].replace(/\\W+(.)/g, (x, chr) => chr.toUpperCase());\n        switch (attribute.value) {\n          case 'true':\n            acc[key] = true;\n            break;\n          case 'false':\n            acc[key] = false;\n            break;\n          case undefined:\n            acc[key] = true;\n            break;\n          default:\n            acc[key] = attribute.value;\n        }\n      }\n      return acc;\n    },\n    {}\n  );\n  return options;\n};\n\nexport function getElementWindow(element) {\n  if (\n    !element ||\n    !element.ownerDocument ||\n    !element.ownerDocument.defaultView\n  ) {\n    return window;\n  }\n  return element.ownerDocument.defaultView;\n}\n\nexport function getElementDocument(element) {\n  if (!element || !element.ownerDocument) {\n    return document;\n  }\n  return element.ownerDocument;\n}\n", "import canUseDOM from 'can-use-dom';\nimport { getElementDocument } from \"./helpers\";\n\nlet cachedScrollbarWidth = null;\nlet cachedDevicePixelRatio = null;\n\nif (canUseDOM) {\n  window.addEventListener('resize', () => {\n    if (cachedDevicePixelRatio !== window.devicePixelRatio) {\n      cachedDevicePixelRatio = window.devicePixelRatio;\n      cachedScrollbarWidth = null;\n    }\n  });\n}\n\nexport default function scrollbarWidth(el) {\n  if (cachedScrollbarWidth === null) {\n    \n    const document = getElementDocument(el);\n    \n    if (typeof document === 'undefined') {\n      cachedScrollbarWidth = 0;\n      return cachedScrollbarWidth;\n    }\n    const body = document.body;\n    const box = document.createElement('div');\n\n    box.classList.add('simplebar-hide-scrollbar');\n\n    body.appendChild(box);\n\n    const width = box.getBoundingClientRect().right;\n\n    body.removeChild(box);\n\n    cachedScrollbarWidth = width;\n  }\n\n  return cachedScrollbarWidth;\n}\n", "import throttle from 'lodash.throttle';\nimport debounce from 'lodash.debounce';\nimport memoize from 'lodash.memoize';\nimport { ResizeObserver } from '@juggle/resize-observer';\nimport canUseDOM from 'can-use-dom';\nimport scrollbarWidth from './scrollbar-width';\nimport { getElementWindow, getElementDocument } from './helpers';\n\nexport default class SimpleBar {\n  constructor(element, options) {\n    this.el = element;\n    this.minScrollbarWidth = 20;\n    this.options = { ...SimpleBar.defaultOptions, ...options };\n    this.classNames = {\n      ...SimpleBar.defaultOptions.classNames,\n      ...this.options.classNames\n    };\n    this.axis = {\n      x: {\n        scrollOffsetAttr: 'scrollLeft',\n        sizeAttr: 'width',\n        scrollSizeAttr: 'scrollWidth',\n        offsetSizeAttr: 'offsetWidth',\n        offsetAttr: 'left',\n        overflowAttr: 'overflowX',\n        dragOffset: 0,\n        isOverflowing: true,\n        isVisible: false,\n        forceVisible: false,\n        track: {},\n        scrollbar: {}\n      },\n      y: {\n        scrollOffsetAttr: 'scrollTop',\n        sizeAttr: 'height',\n        scrollSizeAttr: 'scrollHeight',\n        offsetSizeAttr: 'offsetHeight',\n        offsetAttr: 'top',\n        overflowAttr: 'overflowY',\n        dragOffset: 0,\n        isOverflowing: true,\n        isVisible: false,\n        forceVisible: false,\n        track: {},\n        scrollbar: {}\n      }\n    };\n    this.removePreventClickId = null;\n\n    // Don't re-instantiate over an existing one\n    if (SimpleBar.instances.has(this.el)) {\n      return;\n    }\n\n    this.recalculate = throttle(this.recalculate.bind(this), 64);\n    this.onMouseMove = throttle(this.onMouseMove.bind(this), 64);\n    this.hideScrollbars = debounce(\n      this.hideScrollbars.bind(this),\n      this.options.timeout\n    );\n    this.onWindowResize = debounce(this.onWindowResize.bind(this), 64, {\n      leading: true\n    });\n\n    SimpleBar.getRtlHelpers = memoize(SimpleBar.getRtlHelpers);\n\n    this.init();\n  }\n\n  /**\n   * Static properties\n   */\n\n  /**\n   * Helper to fix browsers inconsistency on RTL:\n   *  - Firefox inverts the scrollbar initial position\n   *  - IE11 inverts both scrollbar position and scrolling offset\n   * Directly inspired by @KingSora's OverlayScrollbars https://github.com/KingSora/OverlayScrollbars/blob/master/js/OverlayScrollbars.js#L1634\n   */\n  static getRtlHelpers() {\n    const dummyDiv = document.createElement('div');\n    dummyDiv.innerHTML =\n      '<div class=\"hs-dummy-scrollbar-size\"><div style=\"height: 200%; width: 200%; margin: 10px 0;\"></div></div>';\n    const scrollbarDummyEl = dummyDiv.firstElementChild;\n    document.body.appendChild(scrollbarDummyEl);\n    const dummyContainerChild = scrollbarDummyEl.firstElementChild;\n    scrollbarDummyEl.scrollLeft = 0;\n    const dummyContainerOffset = SimpleBar.getOffset(scrollbarDummyEl);\n    const dummyContainerChildOffset = SimpleBar.getOffset(dummyContainerChild);\n    scrollbarDummyEl.scrollLeft = 999;\n    const dummyContainerScrollOffsetAfterScroll = SimpleBar.getOffset(\n      dummyContainerChild\n    );\n\n    return {\n      // determines if the scrolling is responding with negative values\n      isRtlScrollingInverted:\n        dummyContainerOffset.left !== dummyContainerChildOffset.left &&\n        dummyContainerChildOffset.left -\n          dummyContainerScrollOffsetAfterScroll.left !==\n          0,\n      // determines if the origin scrollbar position is inverted or not (positioned on left or right)\n      isRtlScrollbarInverted:\n        dummyContainerOffset.left !== dummyContainerChildOffset.left\n    };\n  }\n\n  static defaultOptions = {\n    autoHide: true,\n    forceVisible: false,\n    clickOnTrack: true,\n    clickOnTrackSpeed: 40,\n    classNames: {\n      contentEl: 'simplebar-content',\n      contentWrapper: 'simplebar-content-wrapper',\n      offset: 'simplebar-offset',\n      mask: 'simplebar-mask',\n      wrapper: 'simplebar-wrapper',\n      placeholder: 'simplebar-placeholder',\n      scrollbar: 'simplebar-scrollbar',\n      track: 'simplebar-track',\n      heightAutoObserverWrapperEl: 'simplebar-height-auto-observer-wrapper',\n      heightAutoObserverEl: 'simplebar-height-auto-observer',\n      visible: 'simplebar-visible',\n      horizontal: 'simplebar-horizontal',\n      vertical: 'simplebar-vertical',\n      hover: 'simplebar-hover',\n      dragging: 'simplebar-dragging'\n    },\n    scrollbarMinSize: 25,\n    scrollbarMaxSize: 0,\n    timeout: 1000\n  };\n\n  static getOffset(el) {\n    const rect = el.getBoundingClientRect();\n    const elDocument = getElementDocument(el);\n    const elWindow = getElementWindow(el);\n\n    return {\n      top:\n        rect.top +\n        (elWindow.pageYOffset || elDocument.documentElement.scrollTop),\n      left:\n        rect.left +\n        (elWindow.pageXOffset || elDocument.documentElement.scrollLeft)\n    };\n  }\n\n  static instances = new WeakMap();\n\n  init() {\n    // Save a reference to the instance, so we know this DOM node has already been instancied\n    SimpleBar.instances.set(this.el, this);\n\n    // We stop here on server-side\n    if (canUseDOM) {\n      this.initDOM();\n\n      this.setAccessibilityAttributes();\n\n      this.scrollbarWidth = this.getScrollbarWidth();\n\n      this.recalculate();\n\n      this.initListeners();\n    }\n  }\n\n  initDOM() {\n    // make sure this element doesn't have the elements yet\n    if (\n      Array.prototype.filter.call(this.el.children, child =>\n        child.classList.contains(this.classNames.wrapper)\n      ).length\n    ) {\n      // assume that element has his DOM already initiated\n      this.wrapperEl = this.el.querySelector(`.${this.classNames.wrapper}`);\n      this.contentWrapperEl =\n        this.options.scrollableNode ||\n        this.el.querySelector(`.${this.classNames.contentWrapper}`);\n      this.contentEl =\n        this.options.contentNode ||\n        this.el.querySelector(`.${this.classNames.contentEl}`);\n\n      this.offsetEl = this.el.querySelector(`.${this.classNames.offset}`);\n      this.maskEl = this.el.querySelector(`.${this.classNames.mask}`);\n\n      this.placeholderEl = this.findChild(\n        this.wrapperEl,\n        `.${this.classNames.placeholder}`\n      );\n      this.heightAutoObserverWrapperEl = this.el.querySelector(\n        `.${this.classNames.heightAutoObserverWrapperEl}`\n      );\n      this.heightAutoObserverEl = this.el.querySelector(\n        `.${this.classNames.heightAutoObserverEl}`\n      );\n      this.axis.x.track.el = this.findChild(\n        this.el,\n        `.${this.classNames.track}.${this.classNames.horizontal}`\n      );\n      this.axis.y.track.el = this.findChild(\n        this.el,\n        `.${this.classNames.track}.${this.classNames.vertical}`\n      );\n    } else {\n      // Prepare DOM\n      this.wrapperEl = document.createElement('div');\n      this.contentWrapperEl = document.createElement('div');\n      this.offsetEl = document.createElement('div');\n      this.maskEl = document.createElement('div');\n      this.contentEl = document.createElement('div');\n      this.placeholderEl = document.createElement('div');\n      this.heightAutoObserverWrapperEl = document.createElement('div');\n      this.heightAutoObserverEl = document.createElement('div');\n\n      this.wrapperEl.classList.add(this.classNames.wrapper);\n      this.contentWrapperEl.classList.add(this.classNames.contentWrapper);\n      this.offsetEl.classList.add(this.classNames.offset);\n      this.maskEl.classList.add(this.classNames.mask);\n      this.contentEl.classList.add(this.classNames.contentEl);\n      this.placeholderEl.classList.add(this.classNames.placeholder);\n      this.heightAutoObserverWrapperEl.classList.add(\n        this.classNames.heightAutoObserverWrapperEl\n      );\n      this.heightAutoObserverEl.classList.add(\n        this.classNames.heightAutoObserverEl\n      );\n\n      while (this.el.firstChild) {\n        this.contentEl.appendChild(this.el.firstChild);\n      }\n\n      this.contentWrapperEl.appendChild(this.contentEl);\n      this.offsetEl.appendChild(this.contentWrapperEl);\n      this.maskEl.appendChild(this.offsetEl);\n      this.heightAutoObserverWrapperEl.appendChild(this.heightAutoObserverEl);\n      this.wrapperEl.appendChild(this.heightAutoObserverWrapperEl);\n      this.wrapperEl.appendChild(this.maskEl);\n      this.wrapperEl.appendChild(this.placeholderEl);\n      this.el.appendChild(this.wrapperEl);\n    }\n\n    if (!this.axis.x.track.el || !this.axis.y.track.el) {\n      const track = document.createElement('div');\n      const scrollbar = document.createElement('div');\n\n      track.classList.add(this.classNames.track);\n      scrollbar.classList.add(this.classNames.scrollbar);\n\n      track.appendChild(scrollbar);\n\n      this.axis.x.track.el = track.cloneNode(true);\n      this.axis.x.track.el.classList.add(this.classNames.horizontal);\n\n      this.axis.y.track.el = track.cloneNode(true);\n      this.axis.y.track.el.classList.add(this.classNames.vertical);\n\n      this.el.appendChild(this.axis.x.track.el);\n      this.el.appendChild(this.axis.y.track.el);\n    }\n\n    this.axis.x.scrollbar.el = this.axis.x.track.el.querySelector(\n      `.${this.classNames.scrollbar}`\n    );\n    this.axis.y.scrollbar.el = this.axis.y.track.el.querySelector(\n      `.${this.classNames.scrollbar}`\n    );\n\n    if (!this.options.autoHide) {\n      this.axis.x.scrollbar.el.classList.add(this.classNames.visible);\n      this.axis.y.scrollbar.el.classList.add(this.classNames.visible);\n    }\n\n    this.el.setAttribute('data-simplebar', 'init');\n  }\n\n  setAccessibilityAttributes() {\n    const ariaLabel = this.options.ariaLabel || 'scrollable content';\n\n    this.contentWrapperEl.setAttribute('tabindex', '0');\n    this.contentWrapperEl.setAttribute('role', 'region');\n    this.contentWrapperEl.setAttribute('aria-label', ariaLabel);\n  }\n\n  initListeners() {\n    const elWindow = getElementWindow(this.el);\n    // Event listeners\n    if (this.options.autoHide) {\n      this.el.addEventListener('mouseenter', this.onMouseEnter);\n    }\n\n    ['mousedown', 'click', 'dblclick'].forEach(e => {\n      this.el.addEventListener(e, this.onPointerEvent, true);\n    });\n\n    ['touchstart', 'touchend', 'touchmove'].forEach(e => {\n      this.el.addEventListener(e, this.onPointerEvent, {\n        capture: true,\n        passive: true\n      });\n    });\n\n    this.el.addEventListener('mousemove', this.onMouseMove);\n    this.el.addEventListener('mouseleave', this.onMouseLeave);\n\n    this.contentWrapperEl.addEventListener('scroll', this.onScroll);\n\n    // Browser zoom triggers a window resize\n    elWindow.addEventListener('resize', this.onWindowResize);\n\n    // Hack for https://github.com/WICG/ResizeObserver/issues/38\n    let resizeObserverStarted = false;\n    const resizeObserver = elWindow.ResizeObserver || ResizeObserver;\n    this.resizeObserver = new resizeObserver(() => {\n      if (!resizeObserverStarted) return;\n      this.recalculate();\n    });\n\n    this.resizeObserver.observe(this.el);\n    this.resizeObserver.observe(this.contentEl);\n\n    elWindow.requestAnimationFrame(() => {\n      resizeObserverStarted = true;\n    });\n\n    // This is required to detect horizontal scroll. Vertical scroll only needs the resizeObserver.\n    this.mutationObserver = new elWindow.MutationObserver(this.recalculate);\n\n    this.mutationObserver.observe(this.contentEl, {\n      childList: true,\n      subtree: true,\n      characterData: true\n    });\n  }\n\n  recalculate() {\n    const elWindow = getElementWindow(this.el);\n    this.elStyles = elWindow.getComputedStyle(this.el);\n    this.isRtl = this.elStyles.direction === 'rtl';\n\n    const isHeightAuto = this.heightAutoObserverEl.offsetHeight <= 1;\n    const isWidthAuto = this.heightAutoObserverEl.offsetWidth <= 1;\n    const contentElOffsetWidth = this.contentEl.offsetWidth;\n\n    const contentWrapperElOffsetWidth = this.contentWrapperEl.offsetWidth;\n\n    const elOverflowX = this.elStyles.overflowX;\n    const elOverflowY = this.elStyles.overflowY;\n\n    this.contentEl.style.padding = `${this.elStyles.paddingTop} ${this.elStyles.paddingRight} ${this.elStyles.paddingBottom} ${this.elStyles.paddingLeft}`;\n    this.wrapperEl.style.margin = `-${this.elStyles.paddingTop} -${this.elStyles.paddingRight} -${this.elStyles.paddingBottom} -${this.elStyles.paddingLeft}`;\n\n    const contentElScrollHeight = this.contentEl.scrollHeight;\n    const contentElScrollWidth = this.contentEl.scrollWidth;\n\n    this.contentWrapperEl.style.height = isHeightAuto ? 'auto' : '100%';\n\n    // Determine placeholder size\n    this.placeholderEl.style.width = isWidthAuto\n      ? `${contentElOffsetWidth}px`\n      : 'auto';\n    this.placeholderEl.style.height = `${contentElScrollHeight}px`;\n\n    const contentWrapperElOffsetHeight = this.contentWrapperEl.offsetHeight;\n\n    this.axis.x.isOverflowing = contentElScrollWidth > contentElOffsetWidth;\n    this.axis.y.isOverflowing =\n      contentElScrollHeight > contentWrapperElOffsetHeight;\n\n    // Set isOverflowing to false if user explicitely set hidden overflow\n    this.axis.x.isOverflowing =\n      elOverflowX === 'hidden' ? false : this.axis.x.isOverflowing;\n    this.axis.y.isOverflowing =\n      elOverflowY === 'hidden' ? false : this.axis.y.isOverflowing;\n\n    this.axis.x.forceVisible =\n      this.options.forceVisible === 'x' || this.options.forceVisible === true;\n    this.axis.y.forceVisible =\n      this.options.forceVisible === 'y' || this.options.forceVisible === true;\n\n    this.hideNativeScrollbar();\n\n    // Set isOverflowing to false if scrollbar is not necessary (content is shorter than offset)\n    let offsetForXScrollbar = this.axis.x.isOverflowing\n      ? this.scrollbarWidth\n      : 0;\n    let offsetForYScrollbar = this.axis.y.isOverflowing\n      ? this.scrollbarWidth\n      : 0;\n\n    this.axis.x.isOverflowing =\n      this.axis.x.isOverflowing &&\n      contentElScrollWidth > contentWrapperElOffsetWidth - offsetForYScrollbar;\n    this.axis.y.isOverflowing =\n      this.axis.y.isOverflowing &&\n      contentElScrollHeight >\n        contentWrapperElOffsetHeight - offsetForXScrollbar;\n\n    this.axis.x.scrollbar.size = this.getScrollbarSize('x');\n    this.axis.y.scrollbar.size = this.getScrollbarSize('y');\n\n    this.axis.x.scrollbar.el.style.width = `${this.axis.x.scrollbar.size}px`;\n    this.axis.y.scrollbar.el.style.height = `${this.axis.y.scrollbar.size}px`;\n\n    this.positionScrollbar('x');\n    this.positionScrollbar('y');\n\n    this.toggleTrackVisibility('x');\n    this.toggleTrackVisibility('y');\n  }\n\n  /**\n   * Calculate scrollbar size\n   */\n  getScrollbarSize(axis = 'y') {\n    if (!this.axis[axis].isOverflowing) {\n      return 0;\n    }\n\n    const contentSize = this.contentEl[this.axis[axis].scrollSizeAttr];\n    const trackSize = this.axis[axis].track.el[this.axis[axis].offsetSizeAttr];\n    let scrollbarSize;\n\n    let scrollbarRatio = trackSize / contentSize;\n\n    // Calculate new height/position of drag handle.\n    scrollbarSize = Math.max(\n      ~~(scrollbarRatio * trackSize),\n      this.options.scrollbarMinSize\n    );\n\n    if (this.options.scrollbarMaxSize) {\n      scrollbarSize = Math.min(scrollbarSize, this.options.scrollbarMaxSize);\n    }\n\n    return scrollbarSize;\n  }\n\n  positionScrollbar(axis = 'y') {\n    if (!this.axis[axis].isOverflowing) {\n      return;\n    }\n\n    const contentSize = this.contentWrapperEl[this.axis[axis].scrollSizeAttr];\n    const trackSize = this.axis[axis].track.el[this.axis[axis].offsetSizeAttr];\n    const hostSize = parseInt(this.elStyles[this.axis[axis].sizeAttr], 10);\n    const scrollbar = this.axis[axis].scrollbar;\n\n    let scrollOffset = this.contentWrapperEl[this.axis[axis].scrollOffsetAttr];\n    scrollOffset =\n      axis === 'x' &&\n      this.isRtl &&\n      SimpleBar.getRtlHelpers().isRtlScrollingInverted\n        ? -scrollOffset\n        : scrollOffset;\n    let scrollPourcent = scrollOffset / (contentSize - hostSize);\n\n    let handleOffset = ~~((trackSize - scrollbar.size) * scrollPourcent);\n    handleOffset =\n      axis === 'x' &&\n      this.isRtl &&\n      SimpleBar.getRtlHelpers().isRtlScrollbarInverted\n        ? handleOffset + (trackSize - scrollbar.size)\n        : handleOffset;\n\n    scrollbar.el.style.transform =\n      axis === 'x'\n        ? `translate3d(${handleOffset}px, 0, 0)`\n        : `translate3d(0, ${handleOffset}px, 0)`;\n  }\n\n  toggleTrackVisibility(axis = 'y') {\n    const track = this.axis[axis].track.el;\n    const scrollbar = this.axis[axis].scrollbar.el;\n\n    if (this.axis[axis].isOverflowing || this.axis[axis].forceVisible) {\n      track.style.visibility = 'visible';\n      this.contentWrapperEl.style[this.axis[axis].overflowAttr] = 'scroll';\n    } else {\n      track.style.visibility = 'hidden';\n      this.contentWrapperEl.style[this.axis[axis].overflowAttr] = 'hidden';\n    }\n\n    // Even if forceVisible is enabled, scrollbar itself should be hidden\n    if (this.axis[axis].isOverflowing) {\n      scrollbar.style.display = 'block';\n    } else {\n      scrollbar.style.display = 'none';\n    }\n  }\n\n  hideNativeScrollbar() {\n    this.offsetEl.style[this.isRtl ? 'left' : 'right'] =\n      this.axis.y.isOverflowing || this.axis.y.forceVisible\n        ? `-${this.scrollbarWidth}px`\n        : 0;\n    this.offsetEl.style.bottom =\n      this.axis.x.isOverflowing || this.axis.x.forceVisible\n        ? `-${this.scrollbarWidth}px`\n        : 0;\n  }\n\n  /**\n   * On scroll event handling\n   */\n  onScroll = () => {\n    const elWindow = getElementWindow(this.el);\n    if (!this.scrollXTicking) {\n      elWindow.requestAnimationFrame(this.scrollX);\n      this.scrollXTicking = true;\n    }\n\n    if (!this.scrollYTicking) {\n      elWindow.requestAnimationFrame(this.scrollY);\n      this.scrollYTicking = true;\n    }\n  };\n\n  scrollX = () => {\n    if (this.axis.x.isOverflowing) {\n      this.showScrollbar('x');\n      this.positionScrollbar('x');\n    }\n\n    this.scrollXTicking = false;\n  };\n\n  scrollY = () => {\n    if (this.axis.y.isOverflowing) {\n      this.showScrollbar('y');\n      this.positionScrollbar('y');\n    }\n\n    this.scrollYTicking = false;\n  };\n\n  onMouseEnter = () => {\n    this.showScrollbar('x');\n    this.showScrollbar('y');\n  };\n\n  onMouseMove = e => {\n    this.mouseX = e.clientX;\n    this.mouseY = e.clientY;\n\n    if (this.axis.x.isOverflowing || this.axis.x.forceVisible) {\n      this.onMouseMoveForAxis('x');\n    }\n\n    if (this.axis.y.isOverflowing || this.axis.y.forceVisible) {\n      this.onMouseMoveForAxis('y');\n    }\n  };\n\n  onMouseMoveForAxis(axis = 'y') {\n    this.axis[axis].track.rect = this.axis[\n      axis\n    ].track.el.getBoundingClientRect();\n    this.axis[axis].scrollbar.rect = this.axis[\n      axis\n    ].scrollbar.el.getBoundingClientRect();\n\n    const isWithinScrollbarBoundsX = this.isWithinBounds(\n      this.axis[axis].scrollbar.rect\n    );\n\n    if (isWithinScrollbarBoundsX) {\n      this.axis[axis].scrollbar.el.classList.add(this.classNames.hover);\n    } else {\n      this.axis[axis].scrollbar.el.classList.remove(this.classNames.hover);\n    }\n\n    if (this.isWithinBounds(this.axis[axis].track.rect)) {\n      this.showScrollbar(axis);\n      this.axis[axis].track.el.classList.add(this.classNames.hover);\n    } else {\n      this.axis[axis].track.el.classList.remove(this.classNames.hover);\n    }\n  }\n\n  onMouseLeave = () => {\n    this.onMouseMove.cancel();\n\n    if (this.axis.x.isOverflowing || this.axis.x.forceVisible) {\n      this.onMouseLeaveForAxis('x');\n    }\n\n    if (this.axis.y.isOverflowing || this.axis.y.forceVisible) {\n      this.onMouseLeaveForAxis('y');\n    }\n\n    this.mouseX = -1;\n    this.mouseY = -1;\n  };\n\n  onMouseLeaveForAxis(axis = 'y') {\n    this.axis[axis].track.el.classList.remove(this.classNames.hover);\n    this.axis[axis].scrollbar.el.classList.remove(this.classNames.hover);\n  }\n\n  onWindowResize = () => {\n    // Recalculate scrollbarWidth in case it's a zoom\n    this.scrollbarWidth = this.getScrollbarWidth();\n\n    this.hideNativeScrollbar();\n  };\n\n  /**\n   * Show scrollbar\n   */\n  showScrollbar(axis = 'y') {\n    let scrollbar = this.axis[axis].scrollbar.el;\n\n    if (!this.axis[axis].isVisible) {\n      scrollbar.classList.add(this.classNames.visible);\n      this.axis[axis].isVisible = true;\n    }\n\n    if (this.options.autoHide) {\n      this.hideScrollbars();\n    }\n  }\n\n  /**\n   * Hide Scrollbar\n   */\n  hideScrollbars = () => {\n    this.axis.x.track.rect = this.axis.x.track.el.getBoundingClientRect();\n    this.axis.y.track.rect = this.axis.y.track.el.getBoundingClientRect();\n\n    if (!this.isWithinBounds(this.axis.y.track.rect)) {\n      this.axis.y.scrollbar.el.classList.remove(this.classNames.visible);\n      this.axis.y.isVisible = false;\n    }\n\n    if (!this.isWithinBounds(this.axis.x.track.rect)) {\n      this.axis.x.scrollbar.el.classList.remove(this.classNames.visible);\n      this.axis.x.isVisible = false;\n    }\n  };\n\n  onPointerEvent = e => {\n    let isWithinTrackXBounds, isWithinTrackYBounds;\n\n    this.axis.x.track.rect = this.axis.x.track.el.getBoundingClientRect();\n    this.axis.y.track.rect = this.axis.y.track.el.getBoundingClientRect();\n\n    if (this.axis.x.isOverflowing || this.axis.x.forceVisible) {\n      isWithinTrackXBounds = this.isWithinBounds(this.axis.x.track.rect);\n    }\n\n    if (this.axis.y.isOverflowing || this.axis.y.forceVisible) {\n      isWithinTrackYBounds = this.isWithinBounds(this.axis.y.track.rect);\n    }\n\n    // If any pointer event is called on the scrollbar\n    if (isWithinTrackXBounds || isWithinTrackYBounds) {\n      // Preventing the event's default action stops text being\n      // selectable during the drag.\n      e.preventDefault();\n      // Prevent event leaking\n      e.stopPropagation();\n\n      if (e.type === 'mousedown') {\n        if (isWithinTrackXBounds) {\n          this.axis.x.scrollbar.rect = this.axis.x.scrollbar.el.getBoundingClientRect();\n\n          if (this.isWithinBounds(this.axis.x.scrollbar.rect)) {\n            this.onDragStart(e, 'x');\n          } else {\n            this.onTrackClick(e, 'x');\n          }\n        }\n\n        if (isWithinTrackYBounds) {\n          this.axis.y.scrollbar.rect = this.axis.y.scrollbar.el.getBoundingClientRect();\n\n          if (this.isWithinBounds(this.axis.y.scrollbar.rect)) {\n            this.onDragStart(e, 'y');\n          } else {\n            this.onTrackClick(e, 'y');\n          }\n        }\n      }\n    }\n  };\n\n  /**\n   * on scrollbar handle drag movement starts\n   */\n  onDragStart(e, axis = 'y') {\n    const elDocument = getElementDocument(this.el);\n    const elWindow = getElementWindow(this.el);\n    const scrollbar = this.axis[axis].scrollbar;\n\n    // Measure how far the user's mouse is from the top of the scrollbar drag handle.\n    const eventOffset = axis === 'y' ? e.pageY : e.pageX;\n    this.axis[axis].dragOffset =\n      eventOffset - scrollbar.rect[this.axis[axis].offsetAttr];\n    this.draggedAxis = axis;\n\n    this.el.classList.add(this.classNames.dragging);\n\n    elDocument.addEventListener('mousemove', this.drag, true);\n    elDocument.addEventListener('mouseup', this.onEndDrag, true);\n    if (this.removePreventClickId === null) {\n      elDocument.addEventListener('click', this.preventClick, true);\n      elDocument.addEventListener('dblclick', this.preventClick, true);\n    } else {\n      elWindow.clearTimeout(this.removePreventClickId);\n      this.removePreventClickId = null;\n    }\n  }\n\n  /**\n   * Drag scrollbar handle\n   */\n  drag = e => {\n    let eventOffset;\n    const track = this.axis[this.draggedAxis].track;\n    const trackSize = track.rect[this.axis[this.draggedAxis].sizeAttr];\n    const scrollbar = this.axis[this.draggedAxis].scrollbar;\n    const contentSize = this.contentWrapperEl[\n      this.axis[this.draggedAxis].scrollSizeAttr\n    ];\n    const hostSize = parseInt(\n      this.elStyles[this.axis[this.draggedAxis].sizeAttr],\n      10\n    );\n\n    e.preventDefault();\n    e.stopPropagation();\n\n    if (this.draggedAxis === 'y') {\n      eventOffset = e.pageY;\n    } else {\n      eventOffset = e.pageX;\n    }\n\n    // Calculate how far the user's mouse is from the top/left of the scrollbar (minus the dragOffset).\n    let dragPos =\n      eventOffset -\n      track.rect[this.axis[this.draggedAxis].offsetAttr] -\n      this.axis[this.draggedAxis].dragOffset;\n    // Convert the mouse position into a percentage of the scrollbar height/width.\n    let dragPerc = dragPos / (trackSize - scrollbar.size);\n\n    // Scroll the content by the same percentage.\n    let scrollPos = dragPerc * (contentSize - hostSize);\n\n    // Fix browsers inconsistency on RTL\n    if (this.draggedAxis === 'x') {\n      scrollPos =\n        this.isRtl && SimpleBar.getRtlHelpers().isRtlScrollbarInverted\n          ? scrollPos - (trackSize + scrollbar.size)\n          : scrollPos;\n      scrollPos =\n        this.isRtl && SimpleBar.getRtlHelpers().isRtlScrollingInverted\n          ? -scrollPos\n          : scrollPos;\n    }\n\n    this.contentWrapperEl[\n      this.axis[this.draggedAxis].scrollOffsetAttr\n    ] = scrollPos;\n  };\n\n  /**\n   * End scroll handle drag\n   */\n  onEndDrag = e => {\n    const elDocument = getElementDocument(this.el);\n    const elWindow = getElementWindow(this.el);\n    e.preventDefault();\n    e.stopPropagation();\n\n    this.el.classList.remove(this.classNames.dragging);\n\n    elDocument.removeEventListener('mousemove', this.drag, true);\n    elDocument.removeEventListener('mouseup', this.onEndDrag, true);\n    this.removePreventClickId = elWindow.setTimeout(() => {\n      // Remove these asynchronously so we still suppress click events\n      // generated simultaneously with mouseup.\n      elDocument.removeEventListener('click', this.preventClick, true);\n      elDocument.removeEventListener('dblclick', this.preventClick, true);\n      this.removePreventClickId = null;\n    });\n  };\n\n  /**\n   * Handler to ignore click events during drag\n   */\n  preventClick = e => {\n    e.preventDefault();\n    e.stopPropagation();\n  };\n\n  onTrackClick(e, axis = 'y') {\n    if (!this.options.clickOnTrack) return;\n\n    const elWindow = getElementWindow(this.el);\n    this.axis[axis].scrollbar.rect = this.axis[\n      axis\n    ].scrollbar.el.getBoundingClientRect();\n    const scrollbar = this.axis[axis].scrollbar;\n    const scrollbarOffset = scrollbar.rect[this.axis[axis].offsetAttr];\n    const hostSize = parseInt(this.elStyles[this.axis[axis].sizeAttr], 10);\n    let scrolled = this.contentWrapperEl[this.axis[axis].scrollOffsetAttr];\n    const t =\n      axis === 'y'\n        ? this.mouseY - scrollbarOffset\n        : this.mouseX - scrollbarOffset;\n    const dir = t < 0 ? -1 : 1;\n    const scrollSize = dir === -1 ? scrolled - hostSize : scrolled + hostSize;\n\n    const scrollTo = () => {\n      if (dir === -1) {\n        if (scrolled > scrollSize) {\n          scrolled -= this.options.clickOnTrackSpeed;\n          this.contentWrapperEl.scrollTo({\n            [this.axis[axis].offsetAttr]: scrolled\n          });\n          elWindow.requestAnimationFrame(scrollTo);\n        }\n      } else {\n        if (scrolled < scrollSize) {\n          scrolled += this.options.clickOnTrackSpeed;\n          this.contentWrapperEl.scrollTo({\n            [this.axis[axis].offsetAttr]: scrolled\n          });\n          elWindow.requestAnimationFrame(scrollTo);\n        }\n      }\n    };\n\n    scrollTo();\n  }\n\n  /**\n   * Getter for content element\n   */\n  getContentElement() {\n    return this.contentEl;\n  }\n\n  /**\n   * Getter for original scrolling element\n   */\n  getScrollElement() {\n    return this.contentWrapperEl;\n  }\n\n  getScrollbarWidth() {\n    // Try/catch for FF 56 throwing on undefined computedStyles\n    try {\n      // Detect browsers supporting CSS scrollbar styling and do not calculate\n      if (\n        getComputedStyle(this.contentWrapperEl, '::-webkit-scrollbar')\n          .display === 'none' ||\n        'scrollbarWidth' in document.documentElement.style ||\n        '-ms-overflow-style' in document.documentElement.style\n      ) {\n        return 0;\n      } else {\n        return scrollbarWidth(this.el);\n      }\n    } catch (e) {\n      return scrollbarWidth(this.el);\n    }\n  }\n\n  removeListeners() {\n    const elWindow = getElementWindow(this.el);\n    // Event listeners\n    if (this.options.autoHide) {\n      this.el.removeEventListener('mouseenter', this.onMouseEnter);\n    }\n\n    ['mousedown', 'click', 'dblclick'].forEach(e => {\n      this.el.removeEventListener(e, this.onPointerEvent, true);\n    });\n\n    ['touchstart', 'touchend', 'touchmove'].forEach(e => {\n      this.el.removeEventListener(e, this.onPointerEvent, {\n        capture: true,\n        passive: true\n      });\n    });\n\n    this.el.removeEventListener('mousemove', this.onMouseMove);\n    this.el.removeEventListener('mouseleave', this.onMouseLeave);\n\n    if (this.contentWrapperEl) {\n      this.contentWrapperEl.removeEventListener('scroll', this.onScroll);\n    }\n\n    elWindow.removeEventListener('resize', this.onWindowResize);\n\n    if (this.mutationObserver) {\n      this.mutationObserver.disconnect();\n    }\n\n    if (this.resizeObserver) {\n      this.resizeObserver.disconnect();\n    }\n\n    // Cancel all debounced functions\n    this.recalculate.cancel();\n    this.onMouseMove.cancel();\n    this.hideScrollbars.cancel();\n    this.onWindowResize.cancel();\n  }\n\n  /**\n   * UnMount mutation observer and delete SimpleBar instance from DOM element\n   */\n  unMount() {\n    this.removeListeners();\n    SimpleBar.instances.delete(this.el);\n  }\n\n  /**\n   * Check if mouse is within bounds\n   */\n  isWithinBounds(bbox) {\n    return (\n      this.mouseX >= bbox.left &&\n      this.mouseX <= bbox.left + bbox.width &&\n      this.mouseY >= bbox.top &&\n      this.mouseY <= bbox.top + bbox.height\n    );\n  }\n\n  /**\n   * Find element children matches query\n   */\n  findChild(el, query) {\n    const matches =\n      el.matches ||\n      el.webkitMatchesSelector ||\n      el.mozMatchesSelector ||\n      el.msMatchesSelector;\n    return Array.prototype.filter.call(el.children, child =>\n      matches.call(child, query)\n    )[0];\n  }\n}\n", "import canUseDOM from 'can-use-dom';\n\nimport SimpleBar from './simplebar';\nimport { getOptions } from './helpers';\n\nSimpleBar.initDOMLoadedElements = function() {\n  document.removeEventListener('DOMContentLoaded', this.initDOMLoadedElements);\n  window.removeEventListener('load', this.initDOMLoadedElements);\n\n  Array.prototype.forEach.call(\n    document.querySelectorAll('[data-simplebar]'),\n    el => {\n      if (\n        el.getAttribute('data-simplebar') !== 'init' &&\n        !SimpleBar.instances.has(el)\n      )\n        new SimpleBar(el, getOptions(el.attributes));\n    }\n  );\n};\n\nSimpleBar.removeObserver = function() {\n  this.globalObserver.disconnect();\n};\n\nSimpleBar.initHtmlApi = function() {\n  this.initDOMLoadedElements = this.initDOMLoadedElements.bind(this);\n\n  // MutationObserver is IE11+\n  if (typeof MutationObserver !== 'undefined') {\n    // Mutation observer to observe dynamically added elements\n    this.globalObserver = new MutationObserver(SimpleBar.handleMutations);\n\n    this.globalObserver.observe(document, { childList: true, subtree: true });\n  }\n\n  // Taken from jQuery `ready` function\n  // Instantiate elements already present on the page\n  if (\n    document.readyState === 'complete' ||\n    (document.readyState !== 'loading' && !document.documentElement.doScroll)\n  ) {\n    // Handle it asynchronously to allow scripts the opportunity to delay init\n    window.setTimeout(this.initDOMLoadedElements);\n  } else {\n    document.addEventListener('DOMContentLoaded', this.initDOMLoadedElements);\n    window.addEventListener('load', this.initDOMLoadedElements);\n  }\n};\n\nSimpleBar.handleMutations = mutations => {\n  mutations.forEach(mutation => {\n    Array.prototype.forEach.call(mutation.addedNodes, addedNode => {\n      if (addedNode.nodeType === 1) {\n        if (addedNode.hasAttribute('data-simplebar')) {\n          !SimpleBar.instances.has(addedNode) &&\n            document.documentElement.contains(addedNode) &&\n            new SimpleBar(addedNode, getOptions(addedNode.attributes));\n        } else {\n          Array.prototype.forEach.call(\n            addedNode.querySelectorAll('[data-simplebar]'),\n            function(el) {\n              if (\n                el.getAttribute('data-simplebar') !== 'init' &&\n                !SimpleBar.instances.has(el) &&\n                document.documentElement.contains(el)\n              )\n                new SimpleBar(el, getOptions(el.attributes));\n            }\n          );\n        }\n      }\n    });\n\n    Array.prototype.forEach.call(mutation.removedNodes, removedNode => {\n      if (removedNode.nodeType === 1) {\n        if (removedNode.getAttribute('data-simplebar') === 'init') {\n          SimpleBar.instances.has(removedNode) &&\n            !document.documentElement.contains(removedNode) &&\n            SimpleBar.instances.get(removedNode).unMount();\n        } else {\n          Array.prototype.forEach.call(\n            removedNode.querySelectorAll('[data-simplebar=\"init\"]'),\n            el => {\n              SimpleBar.instances.has(el) &&\n                !document.documentElement.contains(el) &&\n                SimpleBar.instances.get(el).unMount();\n            }\n          );\n        }\n      }\n    });\n  });\n};\n\nSimpleBar.getOptions = getOptions;\n\n/**\n * HTML API\n * Called only in a browser env.\n */\nif (canUseDOM) {\n  SimpleBar.initHtmlApi();\n}\n\nexport default SimpleBar;\n"], "names": ["getOptions", "obj", "options", "Array", "prototype", "reduce", "call", "acc", "attribute", "option", "name", "match", "key", "replace", "x", "chr", "toUpperCase", "value", "undefined", "getElementWindow", "element", "ownerDocument", "defaultView", "window", "getElementDocument", "document", "cachedScrollbarWidth", "cachedDevicePixelRatio", "canUseDOM", "addEventListener", "devicePixelRatio", "scrollbarWidth", "el", "body", "box", "createElement", "classList", "add", "append<PERSON><PERSON><PERSON>", "width", "getBoundingClientRect", "right", "<PERSON><PERSON><PERSON><PERSON>", "SimpleBar", "onScroll", "<PERSON><PERSON><PERSON><PERSON>", "scrollXTicking", "requestAnimationFrame", "scrollX", "scrollYTicking", "scrollY", "axis", "isOverflowing", "showScrollbar", "positionScrollbar", "y", "onMouseEnter", "onMouseMove", "e", "mouseX", "clientX", "mouseY", "clientY", "forceVisible", "onMouseMoveForAxis", "onMouseLeave", "cancel", "onMouseLeaveForAxis", "onWindowResize", "getScrollbarWidth", "hideNativeScrollbar", "hideScrollbars", "track", "rect", "isWithinBounds", "scrollbar", "remove", "classNames", "visible", "isVisible", "onPointerEvent", "isWithinTrackXBounds", "isWithinTrackYBounds", "preventDefault", "stopPropagation", "type", "onDragStart", "onTrackClick", "drag", "eventOffset", "<PERSON><PERSON><PERSON><PERSON>", "trackSize", "sizeAttr", "contentSize", "contentWrapperEl", "scrollSizeAttr", "hostSize", "parseInt", "elStyles", "pageY", "pageX", "dragPos", "offsetAttr", "dragOffset", "dragPerc", "size", "scrollPos", "isRtl", "getRtlHelpers", "isRtlScrollbarInverted", "isRtlScrollingInverted", "scrollOffsetAttr", "onEndDrag", "elDocument", "dragging", "removeEventListener", "removePreventClickId", "setTimeout", "preventClick", "minScrollbarWidth", "defaultOptions", "offsetSizeAttr", "overflowAttr", "instances", "has", "recalculate", "throttle", "bind", "debounce", "timeout", "leading", "memoize", "init", "dummyDiv", "innerHTML", "scrollbarDummyEl", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "du<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scrollLeft", "dummyContainerOffset", "getOffset", "dummyContainerChildOffset", "dummyContainerScrollOffsetAfterScroll", "left", "top", "pageYOffset", "documentElement", "scrollTop", "pageXOffset", "set", "initDOM", "setAccessibilityAttributes", "initListeners", "filter", "children", "child", "contains", "wrapper", "length", "wrapperEl", "querySelector", "scrollableNode", "contentWrapper", "contentEl", "contentNode", "offsetEl", "offset", "maskEl", "mask", "placeholder<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "placeholder", "heightAutoObserverWrapperEl", "heightAutoObserverEl", "horizontal", "vertical", "<PERSON><PERSON><PERSON><PERSON>", "cloneNode", "autoHide", "setAttribute", "aria<PERSON><PERSON><PERSON>", "for<PERSON>ach", "capture", "passive", "resizeObserverStarted", "resizeObserver", "ResizeObserver", "observe", "mutationObserver", "MutationObserver", "childList", "subtree", "characterData", "getComputedStyle", "direction", "isHeightAuto", "offsetHeight", "isWidthAuto", "offsetWidth", "contentElOffsetWidth", "contentWrapperElOffsetWidth", "elOverflowX", "overflowX", "elOverflowY", "overflowY", "style", "padding", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "margin", "contentElScrollHeight", "scrollHeight", "contentElScrollWidth", "scrollWidth", "height", "contentWrapperElOffsetHeight", "offsetForXScrollbar", "offsetForYScrollbar", "getScrollbarSize", "toggleTrackVisibility", "scrollbarSize", "scrollbarRatio", "Math", "max", "scrollbarMinSize", "scrollbarMaxSize", "min", "scrollOffset", "scrollPourcent", "handleOffset", "transform", "visibility", "display", "bottom", "isWithinScrollbarBoundsX", "hover", "clearTimeout", "clickOnTrack", "scrollbarOffset", "scrolled", "t", "dir", "scrollSize", "scrollTo", "clickOnTrackSpeed", "getContentElement", "getScrollElement", "removeListeners", "disconnect", "unMount", "delete", "bbox", "query", "matches", "webkitMatchesSelector", "mozMatchesSelector", "msMatchesSelector", "WeakMap", "initDOMLoadedElements", "querySelectorAll", "getAttribute", "attributes", "removeObserver", "globalObserver", "initHtmlApi", "handleMutations", "readyState", "doScroll", "mutations", "mutation", "addedNodes", "addedNode", "nodeType", "hasAttribute", "removedNodes", "removedNode", "get"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACO,IAAMA,UAAU,GAAG,SAAbA,UAAa,CAASC,GAAT,EAAc;MAChCC,OAAO,GAAGC,KAAK,CAACC,SAAN,CAAgBC,MAAhB,CAAuBC,IAAvB,CACdL,GADc,EAEd,UAACM,GAAD,EAAMC,SAAN,EAAoB;QACZC,MAAM,GAAGD,SAAS,CAACE,IAAV,CAAeC,KAAf,CAAqB,qBAArB,CAAf;;QACIF,MAAJ,EAAY;UACJG,GAAG,GAAGH,MAAM,CAAC,CAAD,CAAN,CAAUI,OAAV,CAAkB,SAAlB,EAA6B,UAACC,CAAD,EAAIC,GAAJ;eAAYA,GAAG,CAACC,WAAJ,EAAZ;OAA7B,CAAZ;;cACQR,SAAS,CAACS,KAAlB;aACO,MAAL;UACEV,GAAG,CAACK,GAAD,CAAH,GAAW,IAAX;;;aAEG,OAAL;UACEL,GAAG,CAACK,GAAD,CAAH,GAAW,KAAX;;;aAEGM,SAAL;UACEX,GAAG,CAACK,GAAD,CAAH,GAAW,IAAX;;;;UAGAL,GAAG,CAACK,GAAD,CAAH,GAAWJ,SAAS,CAACS,KAArB;;;;WAGCV,GAAP;GApBY,EAsBd,EAtBc,CAAhB;SAwBOL,OAAP;CAzBK;AA4BA,SAASiB,gBAAT,CAA0BC,OAA1B,EAAmC;MAEtC,CAACA,OAAD,IACA,CAACA,OAAO,CAACC,aADT,IAEA,CAACD,OAAO,CAACC,aAAR,CAAsBC,WAHzB,EAIE;WACOC,MAAP;;;SAEKH,OAAO,CAACC,aAAR,CAAsBC,WAA7B;;AAGK,SAASE,kBAAT,CAA4BJ,OAA5B,EAAqC;MACtC,CAACA,OAAD,IAAY,CAACA,OAAO,CAACC,aAAzB,EAAwC;WAC/BI,QAAP;;;SAEKL,OAAO,CAACC,aAAf;;;ACzCF,IAAIK,oBAAoB,GAAG,IAA3B;AACA,IAAIC,sBAAsB,GAAG,IAA7B;;AAEA,IAAIC,SAAJ,EAAe;EACbL,MAAM,CAACM,gBAAP,CAAwB,QAAxB,EAAkC,YAAM;QAClCF,sBAAsB,KAAKJ,MAAM,CAACO,gBAAtC,EAAwD;MACtDH,sBAAsB,GAAGJ,MAAM,CAACO,gBAAhC;MACAJ,oBAAoB,GAAG,IAAvB;;GAHJ;;;AAQa,SAASK,cAAT,CAAwBC,EAAxB,EAA4B;MACrCN,oBAAoB,KAAK,IAA7B,EAAmC;QAE3BD,QAAQ,GAAGD,kBAAkB,CAACQ,EAAD,CAAnC;;QAEI,OAAOP,QAAP,KAAoB,WAAxB,EAAqC;MACnCC,oBAAoB,GAAG,CAAvB;aACOA,oBAAP;;;QAEIO,IAAI,GAAGR,QAAQ,CAACQ,IAAtB;QACMC,GAAG,GAAGT,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAAZ;IAEAD,GAAG,CAACE,SAAJ,CAAcC,GAAd,CAAkB,0BAAlB;IAEAJ,IAAI,CAACK,WAAL,CAAiBJ,GAAjB;QAEMK,KAAK,GAAGL,GAAG,CAACM,qBAAJ,GAA4BC,KAA1C;IAEAR,IAAI,CAACS,WAAL,CAAiBR,GAAjB;IAEAR,oBAAoB,GAAGa,KAAvB;;;SAGKb,oBAAP;;;IC9BmBiB;;;qBACPvB,OAAZ,EAAqBlB,OAArB,EAA8B;;;SAkf9B0C,QAlf8B,GAkfnB,YAAM;UACTC,QAAQ,GAAG1B,gBAAgB,CAAC,KAAI,CAACa,EAAN,CAAjC;;UACI,CAAC,KAAI,CAACc,cAAV,EAA0B;QACxBD,QAAQ,CAACE,qBAAT,CAA+B,KAAI,CAACC,OAApC;QACA,KAAI,CAACF,cAAL,GAAsB,IAAtB;;;UAGE,CAAC,KAAI,CAACG,cAAV,EAA0B;QACxBJ,QAAQ,CAACE,qBAAT,CAA+B,KAAI,CAACG,OAApC;QACA,KAAI,CAACD,cAAL,GAAsB,IAAtB;;KA3f0B;;SA+f9BD,OA/f8B,GA+fpB,YAAM;UACV,KAAI,CAACG,IAAL,CAAUrC,CAAV,CAAYsC,aAAhB,EAA+B;QAC7B,KAAI,CAACC,aAAL,CAAmB,GAAnB;;QACA,KAAI,CAACC,iBAAL,CAAuB,GAAvB;;;MAGF,KAAI,CAACR,cAAL,GAAsB,KAAtB;KArgB4B;;SAwgB9BI,OAxgB8B,GAwgBpB,YAAM;UACV,KAAI,CAACC,IAAL,CAAUI,CAAV,CAAYH,aAAhB,EAA+B;QAC7B,KAAI,CAACC,aAAL,CAAmB,GAAnB;;QACA,KAAI,CAACC,iBAAL,CAAuB,GAAvB;;;MAGF,KAAI,CAACL,cAAL,GAAsB,KAAtB;KA9gB4B;;SAihB9BO,YAjhB8B,GAihBf,YAAM;MACnB,KAAI,CAACH,aAAL,CAAmB,GAAnB;;MACA,KAAI,CAACA,aAAL,CAAmB,GAAnB;KAnhB4B;;SAshB9BI,WAthB8B,GAshBhB,UAAAC,CAAC,EAAI;MACjB,KAAI,CAACC,MAAL,GAAcD,CAAC,CAACE,OAAhB;MACA,KAAI,CAACC,MAAL,GAAcH,CAAC,CAACI,OAAhB;;UAEI,KAAI,CAACX,IAAL,CAAUrC,CAAV,CAAYsC,aAAZ,IAA6B,KAAI,CAACD,IAAL,CAAUrC,CAAV,CAAYiD,YAA7C,EAA2D;QACzD,KAAI,CAACC,kBAAL,CAAwB,GAAxB;;;UAGE,KAAI,CAACb,IAAL,CAAUI,CAAV,CAAYH,aAAZ,IAA6B,KAAI,CAACD,IAAL,CAAUI,CAAV,CAAYQ,YAA7C,EAA2D;QACzD,KAAI,CAACC,kBAAL,CAAwB,GAAxB;;KA/hB0B;;SA6jB9BC,YA7jB8B,GA6jBf,YAAM;MACnB,KAAI,CAACR,WAAL,CAAiBS,MAAjB;;UAEI,KAAI,CAACf,IAAL,CAAUrC,CAAV,CAAYsC,aAAZ,IAA6B,KAAI,CAACD,IAAL,CAAUrC,CAAV,CAAYiD,YAA7C,EAA2D;QACzD,KAAI,CAACI,mBAAL,CAAyB,GAAzB;;;UAGE,KAAI,CAAChB,IAAL,CAAUI,CAAV,CAAYH,aAAZ,IAA6B,KAAI,CAACD,IAAL,CAAUI,CAAV,CAAYQ,YAA7C,EAA2D;QACzD,KAAI,CAACI,mBAAL,CAAyB,GAAzB;;;MAGF,KAAI,CAACR,MAAL,GAAc,CAAC,CAAf;MACA,KAAI,CAACE,MAAL,GAAc,CAAC,CAAf;KAzkB4B;;SAilB9BO,cAjlB8B,GAilBb,YAAM;;MAErB,KAAI,CAACrC,cAAL,GAAsB,KAAI,CAACsC,iBAAL,EAAtB;;MAEA,KAAI,CAACC,mBAAL;KArlB4B;;SA2mB9BC,cA3mB8B,GA2mBb,YAAM;MACrB,KAAI,CAACpB,IAAL,CAAUrC,CAAV,CAAY0D,KAAZ,CAAkBC,IAAlB,GAAyB,KAAI,CAACtB,IAAL,CAAUrC,CAAV,CAAY0D,KAAZ,CAAkBxC,EAAlB,CAAqBQ,qBAArB,EAAzB;MACA,KAAI,CAACW,IAAL,CAAUI,CAAV,CAAYiB,KAAZ,CAAkBC,IAAlB,GAAyB,KAAI,CAACtB,IAAL,CAAUI,CAAV,CAAYiB,KAAZ,CAAkBxC,EAAlB,CAAqBQ,qBAArB,EAAzB;;UAEI,CAAC,KAAI,CAACkC,cAAL,CAAoB,KAAI,CAACvB,IAAL,CAAUI,CAAV,CAAYiB,KAAZ,CAAkBC,IAAtC,CAAL,EAAkD;QAChD,KAAI,CAACtB,IAAL,CAAUI,CAAV,CAAYoB,SAAZ,CAAsB3C,EAAtB,CAAyBI,SAAzB,CAAmCwC,MAAnC,CAA0C,KAAI,CAACC,UAAL,CAAgBC,OAA1D;;QACA,KAAI,CAAC3B,IAAL,CAAUI,CAAV,CAAYwB,SAAZ,GAAwB,KAAxB;;;UAGE,CAAC,KAAI,CAACL,cAAL,CAAoB,KAAI,CAACvB,IAAL,CAAUrC,CAAV,CAAY0D,KAAZ,CAAkBC,IAAtC,CAAL,EAAkD;QAChD,KAAI,CAACtB,IAAL,CAAUrC,CAAV,CAAY6D,SAAZ,CAAsB3C,EAAtB,CAAyBI,SAAzB,CAAmCwC,MAAnC,CAA0C,KAAI,CAACC,UAAL,CAAgBC,OAA1D;;QACA,KAAI,CAAC3B,IAAL,CAAUrC,CAAV,CAAYiE,SAAZ,GAAwB,KAAxB;;KAtnB0B;;SA0nB9BC,cA1nB8B,GA0nBb,UAAAtB,CAAC,EAAI;UAChBuB,oBAAJ,EAA0BC,oBAA1B;MAEA,KAAI,CAAC/B,IAAL,CAAUrC,CAAV,CAAY0D,KAAZ,CAAkBC,IAAlB,GAAyB,KAAI,CAACtB,IAAL,CAAUrC,CAAV,CAAY0D,KAAZ,CAAkBxC,EAAlB,CAAqBQ,qBAArB,EAAzB;MACA,KAAI,CAACW,IAAL,CAAUI,CAAV,CAAYiB,KAAZ,CAAkBC,IAAlB,GAAyB,KAAI,CAACtB,IAAL,CAAUI,CAAV,CAAYiB,KAAZ,CAAkBxC,EAAlB,CAAqBQ,qBAArB,EAAzB;;UAEI,KAAI,CAACW,IAAL,CAAUrC,CAAV,CAAYsC,aAAZ,IAA6B,KAAI,CAACD,IAAL,CAAUrC,CAAV,CAAYiD,YAA7C,EAA2D;QACzDkB,oBAAoB,GAAG,KAAI,CAACP,cAAL,CAAoB,KAAI,CAACvB,IAAL,CAAUrC,CAAV,CAAY0D,KAAZ,CAAkBC,IAAtC,CAAvB;;;UAGE,KAAI,CAACtB,IAAL,CAAUI,CAAV,CAAYH,aAAZ,IAA6B,KAAI,CAACD,IAAL,CAAUI,CAAV,CAAYQ,YAA7C,EAA2D;QACzDmB,oBAAoB,GAAG,KAAI,CAACR,cAAL,CAAoB,KAAI,CAACvB,IAAL,CAAUI,CAAV,CAAYiB,KAAZ,CAAkBC,IAAtC,CAAvB;OAXkB;;;UAehBQ,oBAAoB,IAAIC,oBAA5B,EAAkD;;;QAGhDxB,CAAC,CAACyB,cAAF,GAHgD;;QAKhDzB,CAAC,CAAC0B,eAAF;;YAEI1B,CAAC,CAAC2B,IAAF,KAAW,WAAf,EAA4B;cACtBJ,oBAAJ,EAA0B;YACxB,KAAI,CAAC9B,IAAL,CAAUrC,CAAV,CAAY6D,SAAZ,CAAsBF,IAAtB,GAA6B,KAAI,CAACtB,IAAL,CAAUrC,CAAV,CAAY6D,SAAZ,CAAsB3C,EAAtB,CAAyBQ,qBAAzB,EAA7B;;gBAEI,KAAI,CAACkC,cAAL,CAAoB,KAAI,CAACvB,IAAL,CAAUrC,CAAV,CAAY6D,SAAZ,CAAsBF,IAA1C,CAAJ,EAAqD;cACnD,KAAI,CAACa,WAAL,CAAiB5B,CAAjB,EAAoB,GAApB;aADF,MAEO;cACL,KAAI,CAAC6B,YAAL,CAAkB7B,CAAlB,EAAqB,GAArB;;;;cAIAwB,oBAAJ,EAA0B;YACxB,KAAI,CAAC/B,IAAL,CAAUI,CAAV,CAAYoB,SAAZ,CAAsBF,IAAtB,GAA6B,KAAI,CAACtB,IAAL,CAAUI,CAAV,CAAYoB,SAAZ,CAAsB3C,EAAtB,CAAyBQ,qBAAzB,EAA7B;;gBAEI,KAAI,CAACkC,cAAL,CAAoB,KAAI,CAACvB,IAAL,CAAUI,CAAV,CAAYoB,SAAZ,CAAsBF,IAA1C,CAAJ,EAAqD;cACnD,KAAI,CAACa,WAAL,CAAiB5B,CAAjB,EAAoB,GAApB;aADF,MAEO;cACL,KAAI,CAAC6B,YAAL,CAAkB7B,CAAlB,EAAqB,GAArB;;;;;KAjqBoB;;SAssB9B8B,IAtsB8B,GAssBvB,UAAA9B,CAAC,EAAI;UACN+B,WAAJ;UACMjB,KAAK,GAAG,KAAI,CAACrB,IAAL,CAAU,KAAI,CAACuC,WAAf,EAA4BlB,KAA1C;UACMmB,SAAS,GAAGnB,KAAK,CAACC,IAAN,CAAW,KAAI,CAACtB,IAAL,CAAU,KAAI,CAACuC,WAAf,EAA4BE,QAAvC,CAAlB;UACMjB,SAAS,GAAG,KAAI,CAACxB,IAAL,CAAU,KAAI,CAACuC,WAAf,EAA4Bf,SAA9C;UACMkB,WAAW,GAAG,KAAI,CAACC,gBAAL,CAClB,KAAI,CAAC3C,IAAL,CAAU,KAAI,CAACuC,WAAf,EAA4BK,cADV,CAApB;UAGMC,QAAQ,GAAGC,QAAQ,CACvB,KAAI,CAACC,QAAL,CAAc,KAAI,CAAC/C,IAAL,CAAU,KAAI,CAACuC,WAAf,EAA4BE,QAA1C,CADuB,EAEvB,EAFuB,CAAzB;MAKAlC,CAAC,CAACyB,cAAF;MACAzB,CAAC,CAAC0B,eAAF;;UAEI,KAAI,CAACM,WAAL,KAAqB,GAAzB,EAA8B;QAC5BD,WAAW,GAAG/B,CAAC,CAACyC,KAAhB;OADF,MAEO;QACLV,WAAW,GAAG/B,CAAC,CAAC0C,KAAhB;OAnBQ;;;UAuBNC,OAAO,GACTZ,WAAW,GACXjB,KAAK,CAACC,IAAN,CAAW,KAAI,CAACtB,IAAL,CAAU,KAAI,CAACuC,WAAf,EAA4BY,UAAvC,CADA,GAEA,KAAI,CAACnD,IAAL,CAAU,KAAI,CAACuC,WAAf,EAA4Ba,UAH9B,CAvBU;;UA4BNC,QAAQ,GAAGH,OAAO,IAAIV,SAAS,GAAGhB,SAAS,CAAC8B,IAA1B,CAAtB,CA5BU;;UA+BNC,SAAS,GAAGF,QAAQ,IAAIX,WAAW,GAAGG,QAAlB,CAAxB,CA/BU;;UAkCN,KAAI,CAACN,WAAL,KAAqB,GAAzB,EAA8B;QAC5BgB,SAAS,GACP,KAAI,CAACC,KAAL,IAAchE,SAAS,CAACiE,aAAV,GAA0BC,sBAAxC,GACIH,SAAS,IAAIf,SAAS,GAAGhB,SAAS,CAAC8B,IAA1B,CADb,GAEIC,SAHN;QAIAA,SAAS,GACP,KAAI,CAACC,KAAL,IAAchE,SAAS,CAACiE,aAAV,GAA0BE,sBAAxC,GACI,CAACJ,SADL,GAEIA,SAHN;;;MAMF,KAAI,CAACZ,gBAAL,CACE,KAAI,CAAC3C,IAAL,CAAU,KAAI,CAACuC,WAAf,EAA4BqB,gBAD9B,IAEIL,SAFJ;KAnvB4B;;SA2vB9BM,SA3vB8B,GA2vBlB,UAAAtD,CAAC,EAAI;UACTuD,UAAU,GAAGzF,kBAAkB,CAAC,KAAI,CAACQ,EAAN,CAArC;UACMa,QAAQ,GAAG1B,gBAAgB,CAAC,KAAI,CAACa,EAAN,CAAjC;MACA0B,CAAC,CAACyB,cAAF;MACAzB,CAAC,CAAC0B,eAAF;;MAEA,KAAI,CAACpD,EAAL,CAAQI,SAAR,CAAkBwC,MAAlB,CAAyB,KAAI,CAACC,UAAL,CAAgBqC,QAAzC;;MAEAD,UAAU,CAACE,mBAAX,CAA+B,WAA/B,EAA4C,KAAI,CAAC3B,IAAjD,EAAuD,IAAvD;MACAyB,UAAU,CAACE,mBAAX,CAA+B,SAA/B,EAA0C,KAAI,CAACH,SAA/C,EAA0D,IAA1D;MACA,KAAI,CAACI,oBAAL,GAA4BvE,QAAQ,CAACwE,UAAT,CAAoB,YAAM;;;QAGpDJ,UAAU,CAACE,mBAAX,CAA+B,OAA/B,EAAwC,KAAI,CAACG,YAA7C,EAA2D,IAA3D;QACAL,UAAU,CAACE,mBAAX,CAA+B,UAA/B,EAA2C,KAAI,CAACG,YAAhD,EAA8D,IAA9D;QACA,KAAI,CAACF,oBAAL,GAA4B,IAA5B;OAL0B,CAA5B;KArwB4B;;SAixB9BE,YAjxB8B,GAixBf,UAAA5D,CAAC,EAAI;MAClBA,CAAC,CAACyB,cAAF;MACAzB,CAAC,CAAC0B,eAAF;KAnxB4B;;SACvBpD,EAAL,GAAUZ,OAAV;SACKmG,iBAAL,GAAyB,EAAzB;SACKrH,OAAL,qBAAoByC,SAAS,CAAC6E,cAA9B,MAAiDtH,OAAjD;SACK2E,UAAL,qBACKlC,SAAS,CAAC6E,cAAV,CAAyB3C,UAD9B,MAEK,KAAK3E,OAAL,CAAa2E,UAFlB;SAIK1B,IAAL,GAAY;MACVrC,CAAC,EAAE;QACDiG,gBAAgB,EAAE,YADjB;QAEDnB,QAAQ,EAAE,OAFT;QAGDG,cAAc,EAAE,aAHf;QAID0B,cAAc,EAAE,aAJf;QAKDnB,UAAU,EAAE,MALX;QAMDoB,YAAY,EAAE,WANb;QAODnB,UAAU,EAAE,CAPX;QAQDnD,aAAa,EAAE,IARd;QASD2B,SAAS,EAAE,KATV;QAUDhB,YAAY,EAAE,KAVb;QAWDS,KAAK,EAAE,EAXN;QAYDG,SAAS,EAAE;OAbH;MAeVpB,CAAC,EAAE;QACDwD,gBAAgB,EAAE,WADjB;QAEDnB,QAAQ,EAAE,QAFT;QAGDG,cAAc,EAAE,cAHf;QAID0B,cAAc,EAAE,cAJf;QAKDnB,UAAU,EAAE,KALX;QAMDoB,YAAY,EAAE,WANb;QAODnB,UAAU,EAAE,CAPX;QAQDnD,aAAa,EAAE,IARd;QASD2B,SAAS,EAAE,KATV;QAUDhB,YAAY,EAAE,KAVb;QAWDS,KAAK,EAAE,EAXN;QAYDG,SAAS,EAAE;;KA3Bf;SA8BKyC,oBAAL,GAA4B,IAA5B,CAtC4B;;QAyCxBzE,SAAS,CAACgF,SAAV,CAAoBC,GAApB,CAAwB,KAAK5F,EAA7B,CAAJ,EAAsC;;;;SAIjC6F,WAAL,GAAmBC,QAAQ,CAAC,KAAKD,WAAL,CAAiBE,IAAjB,CAAsB,IAAtB,CAAD,EAA8B,EAA9B,CAA3B;SACKtE,WAAL,GAAmBqE,QAAQ,CAAC,KAAKrE,WAAL,CAAiBsE,IAAjB,CAAsB,IAAtB,CAAD,EAA8B,EAA9B,CAA3B;SACKxD,cAAL,GAAsByD,QAAQ,CAC5B,KAAKzD,cAAL,CAAoBwD,IAApB,CAAyB,IAAzB,CAD4B,EAE5B,KAAK7H,OAAL,CAAa+H,OAFe,CAA9B;SAIK7D,cAAL,GAAsB4D,QAAQ,CAAC,KAAK5D,cAAL,CAAoB2D,IAApB,CAAyB,IAAzB,CAAD,EAAiC,EAAjC,EAAqC;MACjEG,OAAO,EAAE;KADmB,CAA9B;IAIAvF,SAAS,CAACiE,aAAV,GAA0BuB,OAAO,CAACxF,SAAS,CAACiE,aAAX,CAAjC;SAEKwB,IAAL;;;;;;;;;;;;;;YAaKxB,gBAAP,yBAAuB;QACfyB,QAAQ,GAAG5G,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAAjB;IACAkG,QAAQ,CAACC,SAAT,GACE,2GADF;QAEMC,gBAAgB,GAAGF,QAAQ,CAACG,iBAAlC;IACA/G,QAAQ,CAACQ,IAAT,CAAcK,WAAd,CAA0BiG,gBAA1B;QACME,mBAAmB,GAAGF,gBAAgB,CAACC,iBAA7C;IACAD,gBAAgB,CAACG,UAAjB,GAA8B,CAA9B;QACMC,oBAAoB,GAAGhG,SAAS,CAACiG,SAAV,CAAoBL,gBAApB,CAA7B;QACMM,yBAAyB,GAAGlG,SAAS,CAACiG,SAAV,CAAoBH,mBAApB,CAAlC;IACAF,gBAAgB,CAACG,UAAjB,GAA8B,GAA9B;QACMI,qCAAqC,GAAGnG,SAAS,CAACiG,SAAV,CAC5CH,mBAD4C,CAA9C;WAIO;;MAEL3B,sBAAsB,EACpB6B,oBAAoB,CAACI,IAArB,KAA8BF,yBAAyB,CAACE,IAAxD,IACAF,yBAAyB,CAACE,IAA1B,GACED,qCAAqC,CAACC,IADxC,KAEE,CANC;;MAQLlC,sBAAsB,EACpB8B,oBAAoB,CAACI,IAArB,KAA8BF,yBAAyB,CAACE;KAT5D;;;YAwCKH,YAAP,mBAAiB5G,EAAjB,EAAqB;QACbyC,IAAI,GAAGzC,EAAE,CAACQ,qBAAH,EAAb;QACMyE,UAAU,GAAGzF,kBAAkB,CAACQ,EAAD,CAArC;QACMa,QAAQ,GAAG1B,gBAAgB,CAACa,EAAD,CAAjC;WAEO;MACLgH,GAAG,EACDvE,IAAI,CAACuE,GAAL,IACCnG,QAAQ,CAACoG,WAAT,IAAwBhC,UAAU,CAACiC,eAAX,CAA2BC,SADpD,CAFG;MAILJ,IAAI,EACFtE,IAAI,CAACsE,IAAL,IACClG,QAAQ,CAACuG,WAAT,IAAwBnC,UAAU,CAACiC,eAAX,CAA2BR,UADpD;KALJ;;;;;SAYFN,OAAA,gBAAO;;IAELzF,SAAS,CAACgF,SAAV,CAAoB0B,GAApB,CAAwB,KAAKrH,EAA7B,EAAiC,IAAjC,EAFK;;QAKDJ,SAAJ,EAAe;WACR0H,OAAL;WAEKC,0BAAL;WAEKxH,cAAL,GAAsB,KAAKsC,iBAAL,EAAtB;WAEKwD,WAAL;WAEK2B,aAAL;;;;SAIJF,UAAA,mBAAU;;;;QAGNnJ,KAAK,CAACC,SAAN,CAAgBqJ,MAAhB,CAAuBnJ,IAAvB,CAA4B,KAAK0B,EAAL,CAAQ0H,QAApC,EAA8C,UAAAC,KAAK;aACjDA,KAAK,CAACvH,SAAN,CAAgBwH,QAAhB,CAAyB,MAAI,CAAC/E,UAAL,CAAgBgF,OAAzC,CADiD;KAAnD,EAEEC,MAHJ,EAIE;;WAEKC,SAAL,GAAiB,KAAK/H,EAAL,CAAQgI,aAAR,OAA0B,KAAKnF,UAAL,CAAgBgF,OAA1C,CAAjB;WACK/D,gBAAL,GACE,KAAK5F,OAAL,CAAa+J,cAAb,IACA,KAAKjI,EAAL,CAAQgI,aAAR,OAA0B,KAAKnF,UAAL,CAAgBqF,cAA1C,CAFF;WAGKC,SAAL,GACE,KAAKjK,OAAL,CAAakK,WAAb,IACA,KAAKpI,EAAL,CAAQgI,aAAR,OAA0B,KAAKnF,UAAL,CAAgBsF,SAA1C,CAFF;WAIKE,QAAL,GAAgB,KAAKrI,EAAL,CAAQgI,aAAR,OAA0B,KAAKnF,UAAL,CAAgByF,MAA1C,CAAhB;WACKC,MAAL,GAAc,KAAKvI,EAAL,CAAQgI,aAAR,OAA0B,KAAKnF,UAAL,CAAgB2F,IAA1C,CAAd;WAEKC,aAAL,GAAqB,KAAKC,SAAL,CACnB,KAAKX,SADc,QAEf,KAAKlF,UAAL,CAAgB8F,WAFD,CAArB;WAIKC,2BAAL,GAAmC,KAAK5I,EAAL,CAAQgI,aAAR,OAC7B,KAAKnF,UAAL,CAAgB+F,2BADa,CAAnC;WAGKC,oBAAL,GAA4B,KAAK7I,EAAL,CAAQgI,aAAR,OACtB,KAAKnF,UAAL,CAAgBgG,oBADM,CAA5B;WAGK1H,IAAL,CAAUrC,CAAV,CAAY0D,KAAZ,CAAkBxC,EAAlB,GAAuB,KAAK0I,SAAL,CACrB,KAAK1I,EADgB,QAEjB,KAAK6C,UAAL,CAAgBL,KAFC,SAEQ,KAAKK,UAAL,CAAgBiG,UAFxB,CAAvB;WAIK3H,IAAL,CAAUI,CAAV,CAAYiB,KAAZ,CAAkBxC,EAAlB,GAAuB,KAAK0I,SAAL,CACrB,KAAK1I,EADgB,QAEjB,KAAK6C,UAAL,CAAgBL,KAFC,SAEQ,KAAKK,UAAL,CAAgBkG,QAFxB,CAAvB;KA/BF,MAmCO;;WAEAhB,SAAL,GAAiBtI,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAAjB;WACK2D,gBAAL,GAAwBrE,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAAxB;WACKkI,QAAL,GAAgB5I,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAAhB;WACKoI,MAAL,GAAc9I,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAAd;WACKgI,SAAL,GAAiB1I,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAAjB;WACKsI,aAAL,GAAqBhJ,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAArB;WACKyI,2BAAL,GAAmCnJ,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAAnC;WACK0I,oBAAL,GAA4BpJ,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAA5B;WAEK4H,SAAL,CAAe3H,SAAf,CAAyBC,GAAzB,CAA6B,KAAKwC,UAAL,CAAgBgF,OAA7C;WACK/D,gBAAL,CAAsB1D,SAAtB,CAAgCC,GAAhC,CAAoC,KAAKwC,UAAL,CAAgBqF,cAApD;WACKG,QAAL,CAAcjI,SAAd,CAAwBC,GAAxB,CAA4B,KAAKwC,UAAL,CAAgByF,MAA5C;WACKC,MAAL,CAAYnI,SAAZ,CAAsBC,GAAtB,CAA0B,KAAKwC,UAAL,CAAgB2F,IAA1C;WACKL,SAAL,CAAe/H,SAAf,CAAyBC,GAAzB,CAA6B,KAAKwC,UAAL,CAAgBsF,SAA7C;WACKM,aAAL,CAAmBrI,SAAnB,CAA6BC,GAA7B,CAAiC,KAAKwC,UAAL,CAAgB8F,WAAjD;WACKC,2BAAL,CAAiCxI,SAAjC,CAA2CC,GAA3C,CACE,KAAKwC,UAAL,CAAgB+F,2BADlB;WAGKC,oBAAL,CAA0BzI,SAA1B,CAAoCC,GAApC,CACE,KAAKwC,UAAL,CAAgBgG,oBADlB;;aAIO,KAAK7I,EAAL,CAAQgJ,UAAf,EAA2B;aACpBb,SAAL,CAAe7H,WAAf,CAA2B,KAAKN,EAAL,CAAQgJ,UAAnC;;;WAGGlF,gBAAL,CAAsBxD,WAAtB,CAAkC,KAAK6H,SAAvC;WACKE,QAAL,CAAc/H,WAAd,CAA0B,KAAKwD,gBAA/B;WACKyE,MAAL,CAAYjI,WAAZ,CAAwB,KAAK+H,QAA7B;WACKO,2BAAL,CAAiCtI,WAAjC,CAA6C,KAAKuI,oBAAlD;WACKd,SAAL,CAAezH,WAAf,CAA2B,KAAKsI,2BAAhC;WACKb,SAAL,CAAezH,WAAf,CAA2B,KAAKiI,MAAhC;WACKR,SAAL,CAAezH,WAAf,CAA2B,KAAKmI,aAAhC;WACKzI,EAAL,CAAQM,WAAR,CAAoB,KAAKyH,SAAzB;;;QAGE,CAAC,KAAK5G,IAAL,CAAUrC,CAAV,CAAY0D,KAAZ,CAAkBxC,EAAnB,IAAyB,CAAC,KAAKmB,IAAL,CAAUI,CAAV,CAAYiB,KAAZ,CAAkBxC,EAAhD,EAAoD;UAC5CwC,KAAK,GAAG/C,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAAd;UACMwC,SAAS,GAAGlD,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAAlB;MAEAqC,KAAK,CAACpC,SAAN,CAAgBC,GAAhB,CAAoB,KAAKwC,UAAL,CAAgBL,KAApC;MACAG,SAAS,CAACvC,SAAV,CAAoBC,GAApB,CAAwB,KAAKwC,UAAL,CAAgBF,SAAxC;MAEAH,KAAK,CAAClC,WAAN,CAAkBqC,SAAlB;WAEKxB,IAAL,CAAUrC,CAAV,CAAY0D,KAAZ,CAAkBxC,EAAlB,GAAuBwC,KAAK,CAACyG,SAAN,CAAgB,IAAhB,CAAvB;WACK9H,IAAL,CAAUrC,CAAV,CAAY0D,KAAZ,CAAkBxC,EAAlB,CAAqBI,SAArB,CAA+BC,GAA/B,CAAmC,KAAKwC,UAAL,CAAgBiG,UAAnD;WAEK3H,IAAL,CAAUI,CAAV,CAAYiB,KAAZ,CAAkBxC,EAAlB,GAAuBwC,KAAK,CAACyG,SAAN,CAAgB,IAAhB,CAAvB;WACK9H,IAAL,CAAUI,CAAV,CAAYiB,KAAZ,CAAkBxC,EAAlB,CAAqBI,SAArB,CAA+BC,GAA/B,CAAmC,KAAKwC,UAAL,CAAgBkG,QAAnD;WAEK/I,EAAL,CAAQM,WAAR,CAAoB,KAAKa,IAAL,CAAUrC,CAAV,CAAY0D,KAAZ,CAAkBxC,EAAtC;WACKA,EAAL,CAAQM,WAAR,CAAoB,KAAKa,IAAL,CAAUI,CAAV,CAAYiB,KAAZ,CAAkBxC,EAAtC;;;SAGGmB,IAAL,CAAUrC,CAAV,CAAY6D,SAAZ,CAAsB3C,EAAtB,GAA2B,KAAKmB,IAAL,CAAUrC,CAAV,CAAY0D,KAAZ,CAAkBxC,EAAlB,CAAqBgI,aAArB,OACrB,KAAKnF,UAAL,CAAgBF,SADK,CAA3B;SAGKxB,IAAL,CAAUI,CAAV,CAAYoB,SAAZ,CAAsB3C,EAAtB,GAA2B,KAAKmB,IAAL,CAAUI,CAAV,CAAYiB,KAAZ,CAAkBxC,EAAlB,CAAqBgI,aAArB,OACrB,KAAKnF,UAAL,CAAgBF,SADK,CAA3B;;QAII,CAAC,KAAKzE,OAAL,CAAagL,QAAlB,EAA4B;WACrB/H,IAAL,CAAUrC,CAAV,CAAY6D,SAAZ,CAAsB3C,EAAtB,CAAyBI,SAAzB,CAAmCC,GAAnC,CAAuC,KAAKwC,UAAL,CAAgBC,OAAvD;WACK3B,IAAL,CAAUI,CAAV,CAAYoB,SAAZ,CAAsB3C,EAAtB,CAAyBI,SAAzB,CAAmCC,GAAnC,CAAuC,KAAKwC,UAAL,CAAgBC,OAAvD;;;SAGG9C,EAAL,CAAQmJ,YAAR,CAAqB,gBAArB,EAAuC,MAAvC;;;SAGF5B,6BAAA,sCAA6B;QACrB6B,SAAS,GAAG,KAAKlL,OAAL,CAAakL,SAAb,IAA0B,oBAA5C;SAEKtF,gBAAL,CAAsBqF,YAAtB,CAAmC,UAAnC,EAA+C,GAA/C;SACKrF,gBAAL,CAAsBqF,YAAtB,CAAmC,MAAnC,EAA2C,QAA3C;SACKrF,gBAAL,CAAsBqF,YAAtB,CAAmC,YAAnC,EAAiDC,SAAjD;;;SAGF5B,gBAAA,yBAAgB;;;QACR3G,QAAQ,GAAG1B,gBAAgB,CAAC,KAAKa,EAAN,CAAjC,CADc;;QAGV,KAAK9B,OAAL,CAAagL,QAAjB,EAA2B;WACpBlJ,EAAL,CAAQH,gBAAR,CAAyB,YAAzB,EAAuC,KAAK2B,YAA5C;;;KAGD,WAAD,EAAc,OAAd,EAAuB,UAAvB,EAAmC6H,OAAnC,CAA2C,UAAA3H,CAAC,EAAI;MAC9C,MAAI,CAAC1B,EAAL,CAAQH,gBAAR,CAAyB6B,CAAzB,EAA4B,MAAI,CAACsB,cAAjC,EAAiD,IAAjD;KADF;KAIC,YAAD,EAAe,UAAf,EAA2B,WAA3B,EAAwCqG,OAAxC,CAAgD,UAAA3H,CAAC,EAAI;MACnD,MAAI,CAAC1B,EAAL,CAAQH,gBAAR,CAAyB6B,CAAzB,EAA4B,MAAI,CAACsB,cAAjC,EAAiD;QAC/CsG,OAAO,EAAE,IADsC;QAE/CC,OAAO,EAAE;OAFX;KADF;SAOKvJ,EAAL,CAAQH,gBAAR,CAAyB,WAAzB,EAAsC,KAAK4B,WAA3C;SACKzB,EAAL,CAAQH,gBAAR,CAAyB,YAAzB,EAAuC,KAAKoC,YAA5C;SAEK6B,gBAAL,CAAsBjE,gBAAtB,CAAuC,QAAvC,EAAiD,KAAKe,QAAtD,EArBc;;IAwBdC,QAAQ,CAAChB,gBAAT,CAA0B,QAA1B,EAAoC,KAAKuC,cAAzC,EAxBc;;QA2BVoH,qBAAqB,GAAG,KAA5B;QACMC,cAAc,GAAG5I,QAAQ,CAAC6I,cAAT,IAA2BA,cAAlD;SACKD,cAAL,GAAsB,IAAIA,cAAJ,CAAmB,YAAM;UACzC,CAACD,qBAAL,EAA4B;;MAC5B,MAAI,CAAC3D,WAAL;KAFoB,CAAtB;SAKK4D,cAAL,CAAoBE,OAApB,CAA4B,KAAK3J,EAAjC;SACKyJ,cAAL,CAAoBE,OAApB,CAA4B,KAAKxB,SAAjC;IAEAtH,QAAQ,CAACE,qBAAT,CAA+B,YAAM;MACnCyI,qBAAqB,GAAG,IAAxB;KADF,EArCc;;SA0CTI,gBAAL,GAAwB,IAAI/I,QAAQ,CAACgJ,gBAAb,CAA8B,KAAKhE,WAAnC,CAAxB;SAEK+D,gBAAL,CAAsBD,OAAtB,CAA8B,KAAKxB,SAAnC,EAA8C;MAC5C2B,SAAS,EAAE,IADiC;MAE5CC,OAAO,EAAE,IAFmC;MAG5CC,aAAa,EAAE;KAHjB;;;SAOFnE,cAAA,uBAAc;QACNhF,QAAQ,GAAG1B,gBAAgB,CAAC,KAAKa,EAAN,CAAjC;SACKkE,QAAL,GAAgBrD,QAAQ,CAACoJ,gBAAT,CAA0B,KAAKjK,EAA/B,CAAhB;SACK2E,KAAL,GAAa,KAAKT,QAAL,CAAcgG,SAAd,KAA4B,KAAzC;QAEMC,YAAY,GAAG,KAAKtB,oBAAL,CAA0BuB,YAA1B,IAA0C,CAA/D;QACMC,WAAW,GAAG,KAAKxB,oBAAL,CAA0ByB,WAA1B,IAAyC,CAA7D;QACMC,oBAAoB,GAAG,KAAKpC,SAAL,CAAemC,WAA5C;QAEME,2BAA2B,GAAG,KAAK1G,gBAAL,CAAsBwG,WAA1D;QAEMG,WAAW,GAAG,KAAKvG,QAAL,CAAcwG,SAAlC;QACMC,WAAW,GAAG,KAAKzG,QAAL,CAAc0G,SAAlC;SAEKzC,SAAL,CAAe0C,KAAf,CAAqBC,OAArB,GAAkC,KAAK5G,QAAL,CAAc6G,UAAhD,SAA8D,KAAK7G,QAAL,CAAc8G,YAA5E,SAA4F,KAAK9G,QAAL,CAAc+G,aAA1G,SAA2H,KAAK/G,QAAL,CAAcgH,WAAzI;SACKnD,SAAL,CAAe8C,KAAf,CAAqBM,MAArB,SAAkC,KAAKjH,QAAL,CAAc6G,UAAhD,UAA+D,KAAK7G,QAAL,CAAc8G,YAA7E,UAA8F,KAAK9G,QAAL,CAAc+G,aAA5G,UAA8H,KAAK/G,QAAL,CAAcgH,WAA5I;QAEME,qBAAqB,GAAG,KAAKjD,SAAL,CAAekD,YAA7C;QACMC,oBAAoB,GAAG,KAAKnD,SAAL,CAAeoD,WAA5C;SAEKzH,gBAAL,CAAsB+G,KAAtB,CAA4BW,MAA5B,GAAqCrB,YAAY,GAAG,MAAH,GAAY,MAA7D,CApBY;;SAuBP1B,aAAL,CAAmBoC,KAAnB,CAAyBtK,KAAzB,GAAiC8J,WAAW,GACrCE,oBADqC,UAExC,MAFJ;SAGK9B,aAAL,CAAmBoC,KAAnB,CAAyBW,MAAzB,GAAqCJ,qBAArC;QAEMK,4BAA4B,GAAG,KAAK3H,gBAAL,CAAsBsG,YAA3D;SAEKjJ,IAAL,CAAUrC,CAAV,CAAYsC,aAAZ,GAA4BkK,oBAAoB,GAAGf,oBAAnD;SACKpJ,IAAL,CAAUI,CAAV,CAAYH,aAAZ,GACEgK,qBAAqB,GAAGK,4BAD1B,CA/BY;;SAmCPtK,IAAL,CAAUrC,CAAV,CAAYsC,aAAZ,GACEqJ,WAAW,KAAK,QAAhB,GAA2B,KAA3B,GAAmC,KAAKtJ,IAAL,CAAUrC,CAAV,CAAYsC,aADjD;SAEKD,IAAL,CAAUI,CAAV,CAAYH,aAAZ,GACEuJ,WAAW,KAAK,QAAhB,GAA2B,KAA3B,GAAmC,KAAKxJ,IAAL,CAAUI,CAAV,CAAYH,aADjD;SAGKD,IAAL,CAAUrC,CAAV,CAAYiD,YAAZ,GACE,KAAK7D,OAAL,CAAa6D,YAAb,KAA8B,GAA9B,IAAqC,KAAK7D,OAAL,CAAa6D,YAAb,KAA8B,IADrE;SAEKZ,IAAL,CAAUI,CAAV,CAAYQ,YAAZ,GACE,KAAK7D,OAAL,CAAa6D,YAAb,KAA8B,GAA9B,IAAqC,KAAK7D,OAAL,CAAa6D,YAAb,KAA8B,IADrE;SAGKO,mBAAL,GA7CY;;QAgDRoJ,mBAAmB,GAAG,KAAKvK,IAAL,CAAUrC,CAAV,CAAYsC,aAAZ,GACtB,KAAKrB,cADiB,GAEtB,CAFJ;QAGI4L,mBAAmB,GAAG,KAAKxK,IAAL,CAAUI,CAAV,CAAYH,aAAZ,GACtB,KAAKrB,cADiB,GAEtB,CAFJ;SAIKoB,IAAL,CAAUrC,CAAV,CAAYsC,aAAZ,GACE,KAAKD,IAAL,CAAUrC,CAAV,CAAYsC,aAAZ,IACAkK,oBAAoB,GAAGd,2BAA2B,GAAGmB,mBAFvD;SAGKxK,IAAL,CAAUI,CAAV,CAAYH,aAAZ,GACE,KAAKD,IAAL,CAAUI,CAAV,CAAYH,aAAZ,IACAgK,qBAAqB,GACnBK,4BAA4B,GAAGC,mBAHnC;SAKKvK,IAAL,CAAUrC,CAAV,CAAY6D,SAAZ,CAAsB8B,IAAtB,GAA6B,KAAKmH,gBAAL,CAAsB,GAAtB,CAA7B;SACKzK,IAAL,CAAUI,CAAV,CAAYoB,SAAZ,CAAsB8B,IAAtB,GAA6B,KAAKmH,gBAAL,CAAsB,GAAtB,CAA7B;SAEKzK,IAAL,CAAUrC,CAAV,CAAY6D,SAAZ,CAAsB3C,EAAtB,CAAyB6K,KAAzB,CAA+BtK,KAA/B,GAA0C,KAAKY,IAAL,CAAUrC,CAAV,CAAY6D,SAAZ,CAAsB8B,IAAhE;SACKtD,IAAL,CAAUI,CAAV,CAAYoB,SAAZ,CAAsB3C,EAAtB,CAAyB6K,KAAzB,CAA+BW,MAA/B,GAA2C,KAAKrK,IAAL,CAAUI,CAAV,CAAYoB,SAAZ,CAAsB8B,IAAjE;SAEKnD,iBAAL,CAAuB,GAAvB;SACKA,iBAAL,CAAuB,GAAvB;SAEKuK,qBAAL,CAA2B,GAA3B;SACKA,qBAAL,CAA2B,GAA3B;;;;;;;SAMFD,mBAAA,0BAAiBzK,IAAjB,EAA6B;QAAZA,IAAY;MAAZA,IAAY,GAAL,GAAK;;;QACvB,CAAC,KAAKA,IAAL,CAAUA,IAAV,EAAgBC,aAArB,EAAoC;aAC3B,CAAP;;;QAGIyC,WAAW,GAAG,KAAKsE,SAAL,CAAe,KAAKhH,IAAL,CAAUA,IAAV,EAAgB4C,cAA/B,CAApB;QACMJ,SAAS,GAAG,KAAKxC,IAAL,CAAUA,IAAV,EAAgBqB,KAAhB,CAAsBxC,EAAtB,CAAyB,KAAKmB,IAAL,CAAUA,IAAV,EAAgBsE,cAAzC,CAAlB;QACIqG,aAAJ;QAEIC,cAAc,GAAGpI,SAAS,GAAGE,WAAjC,CAT2B;;IAY3BiI,aAAa,GAAGE,IAAI,CAACC,GAAL,CACd,CAAC,EAAEF,cAAc,GAAGpI,SAAnB,CADa,EAEd,KAAKzF,OAAL,CAAagO,gBAFC,CAAhB;;QAKI,KAAKhO,OAAL,CAAaiO,gBAAjB,EAAmC;MACjCL,aAAa,GAAGE,IAAI,CAACI,GAAL,CAASN,aAAT,EAAwB,KAAK5N,OAAL,CAAaiO,gBAArC,CAAhB;;;WAGKL,aAAP;;;SAGFxK,oBAAA,2BAAkBH,IAAlB,EAA8B;QAAZA,IAAY;MAAZA,IAAY,GAAL,GAAK;;;QACxB,CAAC,KAAKA,IAAL,CAAUA,IAAV,EAAgBC,aAArB,EAAoC;;;;QAI9ByC,WAAW,GAAG,KAAKC,gBAAL,CAAsB,KAAK3C,IAAL,CAAUA,IAAV,EAAgB4C,cAAtC,CAApB;QACMJ,SAAS,GAAG,KAAKxC,IAAL,CAAUA,IAAV,EAAgBqB,KAAhB,CAAsBxC,EAAtB,CAAyB,KAAKmB,IAAL,CAAUA,IAAV,EAAgBsE,cAAzC,CAAlB;QACMzB,QAAQ,GAAGC,QAAQ,CAAC,KAAKC,QAAL,CAAc,KAAK/C,IAAL,CAAUA,IAAV,EAAgByC,QAA9B,CAAD,EAA0C,EAA1C,CAAzB;QACMjB,SAAS,GAAG,KAAKxB,IAAL,CAAUA,IAAV,EAAgBwB,SAAlC;QAEI0J,YAAY,GAAG,KAAKvI,gBAAL,CAAsB,KAAK3C,IAAL,CAAUA,IAAV,EAAgB4D,gBAAtC,CAAnB;IACAsH,YAAY,GACVlL,IAAI,KAAK,GAAT,IACA,KAAKwD,KADL,IAEAhE,SAAS,CAACiE,aAAV,GAA0BE,sBAF1B,GAGI,CAACuH,YAHL,GAIIA,YALN;QAMIC,cAAc,GAAGD,YAAY,IAAIxI,WAAW,GAAGG,QAAlB,CAAjC;QAEIuI,YAAY,GAAG,CAAC,EAAE,CAAC5I,SAAS,GAAGhB,SAAS,CAAC8B,IAAvB,IAA+B6H,cAAjC,CAApB;IACAC,YAAY,GACVpL,IAAI,KAAK,GAAT,IACA,KAAKwD,KADL,IAEAhE,SAAS,CAACiE,aAAV,GAA0BC,sBAF1B,GAGI0H,YAAY,IAAI5I,SAAS,GAAGhB,SAAS,CAAC8B,IAA1B,CAHhB,GAII8H,YALN;IAOA5J,SAAS,CAAC3C,EAAV,CAAa6K,KAAb,CAAmB2B,SAAnB,GACErL,IAAI,KAAK,GAAT,oBACmBoL,YADnB,qCAEsBA,YAFtB,WADF;;;SAMFV,wBAAA,+BAAsB1K,IAAtB,EAAkC;QAAZA,IAAY;MAAZA,IAAY,GAAL,GAAK;;;QAC1BqB,KAAK,GAAG,KAAKrB,IAAL,CAAUA,IAAV,EAAgBqB,KAAhB,CAAsBxC,EAApC;QACM2C,SAAS,GAAG,KAAKxB,IAAL,CAAUA,IAAV,EAAgBwB,SAAhB,CAA0B3C,EAA5C;;QAEI,KAAKmB,IAAL,CAAUA,IAAV,EAAgBC,aAAhB,IAAiC,KAAKD,IAAL,CAAUA,IAAV,EAAgBY,YAArD,EAAmE;MACjES,KAAK,CAACqI,KAAN,CAAY4B,UAAZ,GAAyB,SAAzB;WACK3I,gBAAL,CAAsB+G,KAAtB,CAA4B,KAAK1J,IAAL,CAAUA,IAAV,EAAgBuE,YAA5C,IAA4D,QAA5D;KAFF,MAGO;MACLlD,KAAK,CAACqI,KAAN,CAAY4B,UAAZ,GAAyB,QAAzB;WACK3I,gBAAL,CAAsB+G,KAAtB,CAA4B,KAAK1J,IAAL,CAAUA,IAAV,EAAgBuE,YAA5C,IAA4D,QAA5D;KAT8B;;;QAa5B,KAAKvE,IAAL,CAAUA,IAAV,EAAgBC,aAApB,EAAmC;MACjCuB,SAAS,CAACkI,KAAV,CAAgB6B,OAAhB,GAA0B,OAA1B;KADF,MAEO;MACL/J,SAAS,CAACkI,KAAV,CAAgB6B,OAAhB,GAA0B,MAA1B;;;;SAIJpK,sBAAA,+BAAsB;SACf+F,QAAL,CAAcwC,KAAd,CAAoB,KAAKlG,KAAL,GAAa,MAAb,GAAsB,OAA1C,IACE,KAAKxD,IAAL,CAAUI,CAAV,CAAYH,aAAZ,IAA6B,KAAKD,IAAL,CAAUI,CAAV,CAAYQ,YAAzC,SACQ,KAAKhC,cADb,UAEI,CAHN;SAIKsI,QAAL,CAAcwC,KAAd,CAAoB8B,MAApB,GACE,KAAKxL,IAAL,CAAUrC,CAAV,CAAYsC,aAAZ,IAA6B,KAAKD,IAAL,CAAUrC,CAAV,CAAYiD,YAAzC,SACQ,KAAKhC,cADb,UAEI,CAHN;;;;;;;SA0DFiC,qBAAA,4BAAmBb,IAAnB,EAA+B;QAAZA,IAAY;MAAZA,IAAY,GAAL,GAAK;;;SACxBA,IAAL,CAAUA,IAAV,EAAgBqB,KAAhB,CAAsBC,IAAtB,GAA6B,KAAKtB,IAAL,CAC3BA,IAD2B,EAE3BqB,KAF2B,CAErBxC,EAFqB,CAElBQ,qBAFkB,EAA7B;SAGKW,IAAL,CAAUA,IAAV,EAAgBwB,SAAhB,CAA0BF,IAA1B,GAAiC,KAAKtB,IAAL,CAC/BA,IAD+B,EAE/BwB,SAF+B,CAErB3C,EAFqB,CAElBQ,qBAFkB,EAAjC;QAIMoM,wBAAwB,GAAG,KAAKlK,cAAL,CAC/B,KAAKvB,IAAL,CAAUA,IAAV,EAAgBwB,SAAhB,CAA0BF,IADK,CAAjC;;QAIImK,wBAAJ,EAA8B;WACvBzL,IAAL,CAAUA,IAAV,EAAgBwB,SAAhB,CAA0B3C,EAA1B,CAA6BI,SAA7B,CAAuCC,GAAvC,CAA2C,KAAKwC,UAAL,CAAgBgK,KAA3D;KADF,MAEO;WACA1L,IAAL,CAAUA,IAAV,EAAgBwB,SAAhB,CAA0B3C,EAA1B,CAA6BI,SAA7B,CAAuCwC,MAAvC,CAA8C,KAAKC,UAAL,CAAgBgK,KAA9D;;;QAGE,KAAKnK,cAAL,CAAoB,KAAKvB,IAAL,CAAUA,IAAV,EAAgBqB,KAAhB,CAAsBC,IAA1C,CAAJ,EAAqD;WAC9CpB,aAAL,CAAmBF,IAAnB;WACKA,IAAL,CAAUA,IAAV,EAAgBqB,KAAhB,CAAsBxC,EAAtB,CAAyBI,SAAzB,CAAmCC,GAAnC,CAAuC,KAAKwC,UAAL,CAAgBgK,KAAvD;KAFF,MAGO;WACA1L,IAAL,CAAUA,IAAV,EAAgBqB,KAAhB,CAAsBxC,EAAtB,CAAyBI,SAAzB,CAAmCwC,MAAnC,CAA0C,KAAKC,UAAL,CAAgBgK,KAA1D;;;;SAmBJ1K,sBAAA,6BAAoBhB,IAApB,EAAgC;QAAZA,IAAY;MAAZA,IAAY,GAAL,GAAK;;;SACzBA,IAAL,CAAUA,IAAV,EAAgBqB,KAAhB,CAAsBxC,EAAtB,CAAyBI,SAAzB,CAAmCwC,MAAnC,CAA0C,KAAKC,UAAL,CAAgBgK,KAA1D;SACK1L,IAAL,CAAUA,IAAV,EAAgBwB,SAAhB,CAA0B3C,EAA1B,CAA6BI,SAA7B,CAAuCwC,MAAvC,CAA8C,KAAKC,UAAL,CAAgBgK,KAA9D;;;;;;SAaFxL,gBAAA,uBAAcF,IAAd,EAA0B;QAAZA,IAAY;MAAZA,IAAY,GAAL,GAAK;;;QACpBwB,SAAS,GAAG,KAAKxB,IAAL,CAAUA,IAAV,EAAgBwB,SAAhB,CAA0B3C,EAA1C;;QAEI,CAAC,KAAKmB,IAAL,CAAUA,IAAV,EAAgB4B,SAArB,EAAgC;MAC9BJ,SAAS,CAACvC,SAAV,CAAoBC,GAApB,CAAwB,KAAKwC,UAAL,CAAgBC,OAAxC;WACK3B,IAAL,CAAUA,IAAV,EAAgB4B,SAAhB,GAA4B,IAA5B;;;QAGE,KAAK7E,OAAL,CAAagL,QAAjB,EAA2B;WACpB3G,cAAL;;;;;;;;;;;SAuEJe,cAAA,qBAAY5B,CAAZ,EAAeP,IAAf,EAA2B;QAAZA,IAAY;MAAZA,IAAY,GAAL,GAAK;;;QACnB8D,UAAU,GAAGzF,kBAAkB,CAAC,KAAKQ,EAAN,CAArC;QACMa,QAAQ,GAAG1B,gBAAgB,CAAC,KAAKa,EAAN,CAAjC;QACM2C,SAAS,GAAG,KAAKxB,IAAL,CAAUA,IAAV,EAAgBwB,SAAlC,CAHyB;;QAMnBc,WAAW,GAAGtC,IAAI,KAAK,GAAT,GAAeO,CAAC,CAACyC,KAAjB,GAAyBzC,CAAC,CAAC0C,KAA/C;SACKjD,IAAL,CAAUA,IAAV,EAAgBoD,UAAhB,GACEd,WAAW,GAAGd,SAAS,CAACF,IAAV,CAAe,KAAKtB,IAAL,CAAUA,IAAV,EAAgBmD,UAA/B,CADhB;SAEKZ,WAAL,GAAmBvC,IAAnB;SAEKnB,EAAL,CAAQI,SAAR,CAAkBC,GAAlB,CAAsB,KAAKwC,UAAL,CAAgBqC,QAAtC;IAEAD,UAAU,CAACpF,gBAAX,CAA4B,WAA5B,EAAyC,KAAK2D,IAA9C,EAAoD,IAApD;IACAyB,UAAU,CAACpF,gBAAX,CAA4B,SAA5B,EAAuC,KAAKmF,SAA5C,EAAuD,IAAvD;;QACI,KAAKI,oBAAL,KAA8B,IAAlC,EAAwC;MACtCH,UAAU,CAACpF,gBAAX,CAA4B,OAA5B,EAAqC,KAAKyF,YAA1C,EAAwD,IAAxD;MACAL,UAAU,CAACpF,gBAAX,CAA4B,UAA5B,EAAwC,KAAKyF,YAA7C,EAA2D,IAA3D;KAFF,MAGO;MACLzE,QAAQ,CAACiM,YAAT,CAAsB,KAAK1H,oBAA3B;WACKA,oBAAL,GAA4B,IAA5B;;;;;;;;SAuFJ7B,eAAA,sBAAa7B,CAAb,EAAgBP,IAAhB,EAA4B;;;QAAZA,IAAY;MAAZA,IAAY,GAAL,GAAK;;;QACtB,CAAC,KAAKjD,OAAL,CAAa6O,YAAlB,EAAgC;QAE1BlM,QAAQ,GAAG1B,gBAAgB,CAAC,KAAKa,EAAN,CAAjC;SACKmB,IAAL,CAAUA,IAAV,EAAgBwB,SAAhB,CAA0BF,IAA1B,GAAiC,KAAKtB,IAAL,CAC/BA,IAD+B,EAE/BwB,SAF+B,CAErB3C,EAFqB,CAElBQ,qBAFkB,EAAjC;QAGMmC,SAAS,GAAG,KAAKxB,IAAL,CAAUA,IAAV,EAAgBwB,SAAlC;QACMqK,eAAe,GAAGrK,SAAS,CAACF,IAAV,CAAe,KAAKtB,IAAL,CAAUA,IAAV,EAAgBmD,UAA/B,CAAxB;QACMN,QAAQ,GAAGC,QAAQ,CAAC,KAAKC,QAAL,CAAc,KAAK/C,IAAL,CAAUA,IAAV,EAAgByC,QAA9B,CAAD,EAA0C,EAA1C,CAAzB;QACIqJ,QAAQ,GAAG,KAAKnJ,gBAAL,CAAsB,KAAK3C,IAAL,CAAUA,IAAV,EAAgB4D,gBAAtC,CAAf;QACMmI,CAAC,GACL/L,IAAI,KAAK,GAAT,GACI,KAAKU,MAAL,GAAcmL,eADlB,GAEI,KAAKrL,MAAL,GAAcqL,eAHpB;QAIMG,GAAG,GAAGD,CAAC,GAAG,CAAJ,GAAQ,CAAC,CAAT,GAAa,CAAzB;QACME,UAAU,GAAGD,GAAG,KAAK,CAAC,CAAT,GAAaF,QAAQ,GAAGjJ,QAAxB,GAAmCiJ,QAAQ,GAAGjJ,QAAjE;;QAEMqJ,QAAQ,GAAG,SAAXA,QAAW,GAAM;UACjBF,GAAG,KAAK,CAAC,CAAb,EAAgB;YACVF,QAAQ,GAAGG,UAAf,EAA2B;;;UACzBH,QAAQ,IAAI,MAAI,CAAC/O,OAAL,CAAaoP,iBAAzB;;UACA,MAAI,CAACxJ,gBAAL,CAAsBuJ,QAAtB,oDACG,MAAI,CAAClM,IAAL,CAAUA,IAAV,EAAgBmD,UADnB,IACgC2I,QADhC;;UAGApM,QAAQ,CAACE,qBAAT,CAA+BsM,QAA/B;;OANJ,MAQO;YACDJ,QAAQ,GAAGG,UAAf,EAA2B;;;UACzBH,QAAQ,IAAI,MAAI,CAAC/O,OAAL,CAAaoP,iBAAzB;;UACA,MAAI,CAACxJ,gBAAL,CAAsBuJ,QAAtB,sDACG,MAAI,CAAClM,IAAL,CAAUA,IAAV,EAAgBmD,UADnB,IACgC2I,QADhC;;UAGApM,QAAQ,CAACE,qBAAT,CAA+BsM,QAA/B;;;KAfN;;IAoBAA,QAAQ;;;;;;;SAMVE,oBAAA,6BAAoB;WACX,KAAKpF,SAAZ;;;;;;;SAMFqF,mBAAA,4BAAmB;WACV,KAAK1J,gBAAZ;;;SAGFzB,oBAAA,6BAAoB;;QAEd;;UAGA4H,gBAAgB,CAAC,KAAKnG,gBAAN,EAAwB,qBAAxB,CAAhB,CACG4I,OADH,KACe,MADf,IAEA,oBAAoBjN,QAAQ,CAACyH,eAAT,CAAyB2D,KAF7C,IAGA,wBAAwBpL,QAAQ,CAACyH,eAAT,CAAyB2D,KAJnD,EAKE;eACO,CAAP;OANF,MAOO;eACE9K,cAAc,CAAC,KAAKC,EAAN,CAArB;;KAVJ,CAYE,OAAO0B,CAAP,EAAU;aACH3B,cAAc,CAAC,KAAKC,EAAN,CAArB;;;;SAIJyN,kBAAA,2BAAkB;;;QACV5M,QAAQ,GAAG1B,gBAAgB,CAAC,KAAKa,EAAN,CAAjC,CADgB;;QAGZ,KAAK9B,OAAL,CAAagL,QAAjB,EAA2B;WACpBlJ,EAAL,CAAQmF,mBAAR,CAA4B,YAA5B,EAA0C,KAAK3D,YAA/C;;;KAGD,WAAD,EAAc,OAAd,EAAuB,UAAvB,EAAmC6H,OAAnC,CAA2C,UAAA3H,CAAC,EAAI;MAC9C,MAAI,CAAC1B,EAAL,CAAQmF,mBAAR,CAA4BzD,CAA5B,EAA+B,MAAI,CAACsB,cAApC,EAAoD,IAApD;KADF;KAIC,YAAD,EAAe,UAAf,EAA2B,WAA3B,EAAwCqG,OAAxC,CAAgD,UAAA3H,CAAC,EAAI;MACnD,MAAI,CAAC1B,EAAL,CAAQmF,mBAAR,CAA4BzD,CAA5B,EAA+B,MAAI,CAACsB,cAApC,EAAoD;QAClDsG,OAAO,EAAE,IADyC;QAElDC,OAAO,EAAE;OAFX;KADF;SAOKvJ,EAAL,CAAQmF,mBAAR,CAA4B,WAA5B,EAAyC,KAAK1D,WAA9C;SACKzB,EAAL,CAAQmF,mBAAR,CAA4B,YAA5B,EAA0C,KAAKlD,YAA/C;;QAEI,KAAK6B,gBAAT,EAA2B;WACpBA,gBAAL,CAAsBqB,mBAAtB,CAA0C,QAA1C,EAAoD,KAAKvE,QAAzD;;;IAGFC,QAAQ,CAACsE,mBAAT,CAA6B,QAA7B,EAAuC,KAAK/C,cAA5C;;QAEI,KAAKwH,gBAAT,EAA2B;WACpBA,gBAAL,CAAsB8D,UAAtB;;;QAGE,KAAKjE,cAAT,EAAyB;WAClBA,cAAL,CAAoBiE,UAApB;KAhCc;;;SAoCX7H,WAAL,CAAiB3D,MAAjB;SACKT,WAAL,CAAiBS,MAAjB;SACKK,cAAL,CAAoBL,MAApB;SACKE,cAAL,CAAoBF,MAApB;;;;;;;SAMFyL,UAAA,mBAAU;SACHF,eAAL;IACA9M,SAAS,CAACgF,SAAV,CAAoBiI,MAApB,CAA2B,KAAK5N,EAAhC;;;;;;;SAMF0C,iBAAA,wBAAemL,IAAf,EAAqB;WAEjB,KAAKlM,MAAL,IAAekM,IAAI,CAAC9G,IAApB,IACA,KAAKpF,MAAL,IAAekM,IAAI,CAAC9G,IAAL,GAAY8G,IAAI,CAACtN,KADhC,IAEA,KAAKsB,MAAL,IAAegM,IAAI,CAAC7G,GAFpB,IAGA,KAAKnF,MAAL,IAAegM,IAAI,CAAC7G,GAAL,GAAW6G,IAAI,CAACrC,MAJjC;;;;;;;SAWF9C,YAAA,mBAAU1I,EAAV,EAAc8N,KAAd,EAAqB;QACbC,OAAO,GACX/N,EAAE,CAAC+N,OAAH,IACA/N,EAAE,CAACgO,qBADH,IAEAhO,EAAE,CAACiO,kBAFH,IAGAjO,EAAE,CAACkO,iBAJL;WAKO/P,KAAK,CAACC,SAAN,CAAgBqJ,MAAhB,CAAuBnJ,IAAvB,CAA4B0B,EAAE,CAAC0H,QAA/B,EAAyC,UAAAC,KAAK;aACnDoG,OAAO,CAACzP,IAAR,CAAaqJ,KAAb,EAAoBmG,KAApB,CADmD;KAA9C,EAEL,CAFK,CAAP;;;;;;AAx6BiBnN,UAmGZ6E,iBAAiB;EACtB0D,QAAQ,EAAE,IADY;EAEtBnH,YAAY,EAAE,KAFQ;EAGtBgL,YAAY,EAAE,IAHQ;EAItBO,iBAAiB,EAAE,EAJG;EAKtBzK,UAAU,EAAE;IACVsF,SAAS,EAAE,mBADD;IAEVD,cAAc,EAAE,2BAFN;IAGVI,MAAM,EAAE,kBAHE;IAIVE,IAAI,EAAE,gBAJI;IAKVX,OAAO,EAAE,mBALC;IAMVc,WAAW,EAAE,uBANH;IAOVhG,SAAS,EAAE,qBAPD;IAQVH,KAAK,EAAE,iBARG;IASVoG,2BAA2B,EAAE,wCATnB;IAUVC,oBAAoB,EAAE,gCAVZ;IAWV/F,OAAO,EAAE,mBAXC;IAYVgG,UAAU,EAAE,sBAZF;IAaVC,QAAQ,EAAE,oBAbA;IAcV8D,KAAK,EAAE,iBAdG;IAeV3H,QAAQ,EAAE;GApBU;EAsBtBgH,gBAAgB,EAAE,EAtBI;EAuBtBC,gBAAgB,EAAE,CAvBI;EAwBtBlG,OAAO,EAAE;;AA3HQtF,UA6IZgF,YAAY,IAAIwI,OAAJ;;AChJrBxN,SAAS,CAACyN,qBAAV,GAAkC,YAAW;EAC3C3O,QAAQ,CAAC0F,mBAAT,CAA6B,kBAA7B,EAAiD,KAAKiJ,qBAAtD;EACA7O,MAAM,CAAC4F,mBAAP,CAA2B,MAA3B,EAAmC,KAAKiJ,qBAAxC;EAEAjQ,KAAK,CAACC,SAAN,CAAgBiL,OAAhB,CAAwB/K,IAAxB,CACEmB,QAAQ,CAAC4O,gBAAT,CAA0B,kBAA1B,CADF,EAEE,UAAArO,EAAE,EAAI;QAEFA,EAAE,CAACsO,YAAH,CAAgB,gBAAhB,MAAsC,MAAtC,IACA,CAAC3N,SAAS,CAACgF,SAAV,CAAoBC,GAApB,CAAwB5F,EAAxB,CAFH,EAIE,IAAIW,SAAJ,CAAcX,EAAd,EAAkBhC,UAAU,CAACgC,EAAE,CAACuO,UAAJ,CAA5B;GAPN;CAJF;;AAgBA5N,SAAS,CAAC6N,cAAV,GAA2B,YAAW;OAC/BC,cAAL,CAAoBf,UAApB;CADF;;AAIA/M,SAAS,CAAC+N,WAAV,GAAwB,YAAW;OAC5BN,qBAAL,GAA6B,KAAKA,qBAAL,CAA2BrI,IAA3B,CAAgC,IAAhC,CAA7B,CADiC;;MAI7B,OAAO8D,gBAAP,KAA4B,WAAhC,EAA6C;;SAEtC4E,cAAL,GAAsB,IAAI5E,gBAAJ,CAAqBlJ,SAAS,CAACgO,eAA/B,CAAtB;SAEKF,cAAL,CAAoB9E,OAApB,CAA4BlK,QAA5B,EAAsC;MAAEqK,SAAS,EAAE,IAAb;MAAmBC,OAAO,EAAE;KAAlE;GAR+B;;;;MAc/BtK,QAAQ,CAACmP,UAAT,KAAwB,UAAxB,IACCnP,QAAQ,CAACmP,UAAT,KAAwB,SAAxB,IAAqC,CAACnP,QAAQ,CAACyH,eAAT,CAAyB2H,QAFlE,EAGE;;IAEAtP,MAAM,CAAC8F,UAAP,CAAkB,KAAK+I,qBAAvB;GALF,MAMO;IACL3O,QAAQ,CAACI,gBAAT,CAA0B,kBAA1B,EAA8C,KAAKuO,qBAAnD;IACA7O,MAAM,CAACM,gBAAP,CAAwB,MAAxB,EAAgC,KAAKuO,qBAArC;;CArBJ;;AAyBAzN,SAAS,CAACgO,eAAV,GAA4B,UAAAG,SAAS,EAAI;EACvCA,SAAS,CAACzF,OAAV,CAAkB,UAAA0F,QAAQ,EAAI;IAC5B5Q,KAAK,CAACC,SAAN,CAAgBiL,OAAhB,CAAwB/K,IAAxB,CAA6ByQ,QAAQ,CAACC,UAAtC,EAAkD,UAAAC,SAAS,EAAI;UACzDA,SAAS,CAACC,QAAV,KAAuB,CAA3B,EAA8B;YACxBD,SAAS,CAACE,YAAV,CAAuB,gBAAvB,CAAJ,EAA8C;WAC3CxO,SAAS,CAACgF,SAAV,CAAoBC,GAApB,CAAwBqJ,SAAxB,CAAD,IACExP,QAAQ,CAACyH,eAAT,CAAyBU,QAAzB,CAAkCqH,SAAlC,CADF,IAEE,IAAItO,SAAJ,CAAcsO,SAAd,EAAyBjR,UAAU,CAACiR,SAAS,CAACV,UAAX,CAAnC,CAFF;SADF,MAIO;UACLpQ,KAAK,CAACC,SAAN,CAAgBiL,OAAhB,CAAwB/K,IAAxB,CACE2Q,SAAS,CAACZ,gBAAV,CAA2B,kBAA3B,CADF,EAEE,UAASrO,EAAT,EAAa;gBAETA,EAAE,CAACsO,YAAH,CAAgB,gBAAhB,MAAsC,MAAtC,IACA,CAAC3N,SAAS,CAACgF,SAAV,CAAoBC,GAApB,CAAwB5F,EAAxB,CADD,IAEAP,QAAQ,CAACyH,eAAT,CAAyBU,QAAzB,CAAkC5H,EAAlC,CAHF,EAKE,IAAIW,SAAJ,CAAcX,EAAd,EAAkBhC,UAAU,CAACgC,EAAE,CAACuO,UAAJ,CAA5B;WARN;;;KAPN;IAsBApQ,KAAK,CAACC,SAAN,CAAgBiL,OAAhB,CAAwB/K,IAAxB,CAA6ByQ,QAAQ,CAACK,YAAtC,EAAoD,UAAAC,WAAW,EAAI;UAC7DA,WAAW,CAACH,QAAZ,KAAyB,CAA7B,EAAgC;YAC1BG,WAAW,CAACf,YAAZ,CAAyB,gBAAzB,MAA+C,MAAnD,EAA2D;UACzD3N,SAAS,CAACgF,SAAV,CAAoBC,GAApB,CAAwByJ,WAAxB,KACE,CAAC5P,QAAQ,CAACyH,eAAT,CAAyBU,QAAzB,CAAkCyH,WAAlC,CADH,IAEE1O,SAAS,CAACgF,SAAV,CAAoB2J,GAApB,CAAwBD,WAAxB,EAAqC1B,OAArC,EAFF;SADF,MAIO;UACLxP,KAAK,CAACC,SAAN,CAAgBiL,OAAhB,CAAwB/K,IAAxB,CACE+Q,WAAW,CAAChB,gBAAZ,CAA6B,yBAA7B,CADF,EAEE,UAAArO,EAAE,EAAI;YACJW,SAAS,CAACgF,SAAV,CAAoBC,GAApB,CAAwB5F,EAAxB,KACE,CAACP,QAAQ,CAACyH,eAAT,CAAyBU,QAAzB,CAAkC5H,EAAlC,CADH,IAEEW,SAAS,CAACgF,SAAV,CAAoB2J,GAApB,CAAwBtP,EAAxB,EAA4B2N,OAA5B,EAFF;WAHJ;;;KAPN;GAvBF;CADF;;AA6CAhN,SAAS,CAAC3C,UAAV,GAAuBA,UAAvB;;;;;;AAMA,IAAI4B,SAAJ,EAAe;EACbe,SAAS,CAAC+N,WAAV;;;"}