{"version": 3, "sources": ["../../../../../node_modules/ngx-webcam/fesm2020/ngx-webcam.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Component, Input, Output, ViewChild, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\n/**\r\n * Container class for a captured webcam image\r\n * <AUTHOR> davidshen84\r\n */\nconst _c0 = [\"video\"];\nconst _c1 = [\"canvas\"];\nfunction WebcamComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵlistener(\"click\", function WebcamComponent_div_3_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.rotateVideoInput(true));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nclass WebcamImage {\n  constructor(imageAsDataUrl, mimeType, imageData) {\n    this._mimeType = null;\n    this._imageAsBase64 = null;\n    this._imageAsDataUrl = null;\n    this._imageData = null;\n    this._mimeType = mimeType;\n    this._imageAsDataUrl = imageAsDataUrl;\n    this._imageData = imageData;\n  }\n  /**\r\n   * Extracts the Base64 data out of the given dataUrl.\r\n   * @param dataUrl the given dataUrl\r\n   * @param mimeType the mimeType of the data\r\n   */\n  static getDataFromDataUrl(dataUrl, mimeType) {\n    return dataUrl.replace(`data:${mimeType};base64,`, '');\n  }\n  /**\r\n   * Get the base64 encoded image data\r\n   * @returns base64 data of the image\r\n   */\n  get imageAsBase64() {\n    return this._imageAsBase64 ? this._imageAsBase64 : this._imageAsBase64 = WebcamImage.getDataFromDataUrl(this._imageAsDataUrl, this._mimeType);\n  }\n  /**\r\n   * Get the encoded image as dataUrl\r\n   * @returns the dataUrl of the image\r\n   */\n  get imageAsDataUrl() {\n    return this._imageAsDataUrl;\n  }\n  /**\r\n   * Get the ImageData object associated with the canvas' 2d context.\r\n   * @returns the ImageData of the canvas's 2d context.\r\n   */\n  get imageData() {\n    return this._imageData;\n  }\n}\nclass WebcamUtil {\n  /**\r\n   * Lists available videoInput devices\r\n   * @returns a list of media device info.\r\n   */\n  static getAvailableVideoInputs() {\n    if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {\n      return Promise.reject('enumerateDevices() not supported.');\n    }\n    return new Promise((resolve, reject) => {\n      navigator.mediaDevices.enumerateDevices().then(devices => {\n        resolve(devices.filter(device => device.kind === 'videoinput'));\n      }).catch(err => {\n        reject(err.message || err);\n      });\n    });\n  }\n}\nclass WebcamComponent {\n  constructor() {\n    /** Defines the max width of the webcam area in px */\n    this.width = 640;\n    /** Defines the max height of the webcam area in px */\n    this.height = 480;\n    /** Defines base constraints to apply when requesting video track from UserMedia */\n    this.videoOptions = WebcamComponent.DEFAULT_VIDEO_OPTIONS;\n    /** Flag to enable/disable camera switch. If enabled, a switch icon will be displayed if multiple cameras were found */\n    this.allowCameraSwitch = true;\n    /** Flag to control whether an ImageData object is stored into the WebcamImage object. */\n    this.captureImageData = false;\n    /** The image type to use when capturing snapshots */\n    this.imageType = WebcamComponent.DEFAULT_IMAGE_TYPE;\n    /** The image quality to use when capturing snapshots (number between 0 and 1) */\n    this.imageQuality = WebcamComponent.DEFAULT_IMAGE_QUALITY;\n    /** EventEmitter which fires when an image has been captured */\n    this.imageCapture = new EventEmitter();\n    /** Emits a mediaError if webcam cannot be initialized (e.g. missing user permissions) */\n    this.initError = new EventEmitter();\n    /** Emits when the webcam video was clicked */\n    this.imageClick = new EventEmitter();\n    /** Emits the active deviceId after the active video device was switched */\n    this.cameraSwitched = new EventEmitter();\n    /** available video devices */\n    this.availableVideoInputs = [];\n    /** Indicates whether the video device is ready to be switched */\n    this.videoInitialized = false;\n    /** Index of active video in availableVideoInputs */\n    this.activeVideoInputIndex = -1;\n    /** MediaStream object in use for streaming UserMedia data */\n    this.mediaStream = null;\n    /** width and height of the active video stream */\n    this.activeVideoSettings = null;\n  }\n  /**\r\n   * If the given Observable emits, an image will be captured and emitted through 'imageCapture' EventEmitter\r\n   */\n  set trigger(trigger) {\n    if (this.triggerSubscription) {\n      this.triggerSubscription.unsubscribe();\n    }\n    // Subscribe to events from this Observable to take snapshots\n    this.triggerSubscription = trigger.subscribe(() => {\n      this.takeSnapshot();\n    });\n  }\n  /**\r\n   * If the given Observable emits, the active webcam will be switched to the one indicated by the emitted value.\r\n   * @param switchCamera Indicates which webcam to switch to\r\n   *   true: cycle forwards through available webcams\r\n   *   false: cycle backwards through available webcams\r\n   *   string: activate the webcam with the given id\r\n   */\n  set switchCamera(switchCamera) {\n    if (this.switchCameraSubscription) {\n      this.switchCameraSubscription.unsubscribe();\n    }\n    // Subscribe to events from this Observable to switch video device\n    this.switchCameraSubscription = switchCamera.subscribe(value => {\n      if (typeof value === 'string') {\n        // deviceId was specified\n        this.switchToVideoInput(value);\n      } else {\n        // direction was specified\n        this.rotateVideoInput(value !== false);\n      }\n    });\n  }\n  /**\r\n   * Get MediaTrackConstraints to request streaming the given device\r\n   * @param deviceId\r\n   * @param baseMediaTrackConstraints base constraints to merge deviceId-constraint into\r\n   * @returns\r\n   */\n  static getMediaConstraintsForDevice(deviceId, baseMediaTrackConstraints) {\n    const result = baseMediaTrackConstraints ? baseMediaTrackConstraints : this.DEFAULT_VIDEO_OPTIONS;\n    if (deviceId) {\n      result.deviceId = {\n        exact: deviceId\n      };\n    }\n    return result;\n  }\n  /**\r\n   * Tries to harvest the deviceId from the given mediaStreamTrack object.\r\n   * Browsers populate this object differently; this method tries some different approaches\r\n   * to read the id.\r\n   * @param mediaStreamTrack\r\n   * @returns deviceId if found in the mediaStreamTrack\r\n   */\n  static getDeviceIdFromMediaStreamTrack(mediaStreamTrack) {\n    if (mediaStreamTrack.getSettings && mediaStreamTrack.getSettings() && mediaStreamTrack.getSettings().deviceId) {\n      return mediaStreamTrack.getSettings().deviceId;\n    } else if (mediaStreamTrack.getConstraints && mediaStreamTrack.getConstraints() && mediaStreamTrack.getConstraints().deviceId) {\n      const deviceIdObj = mediaStreamTrack.getConstraints().deviceId;\n      return WebcamComponent.getValueFromConstrainDOMString(deviceIdObj);\n    }\n  }\n  /**\r\n   * Tries to harvest the facingMode from the given mediaStreamTrack object.\r\n   * Browsers populate this object differently; this method tries some different approaches\r\n   * to read the value.\r\n   * @param mediaStreamTrack\r\n   * @returns facingMode if found in the mediaStreamTrack\r\n   */\n  static getFacingModeFromMediaStreamTrack(mediaStreamTrack) {\n    if (mediaStreamTrack) {\n      if (mediaStreamTrack.getSettings && mediaStreamTrack.getSettings() && mediaStreamTrack.getSettings().facingMode) {\n        return mediaStreamTrack.getSettings().facingMode;\n      } else if (mediaStreamTrack.getConstraints && mediaStreamTrack.getConstraints() && mediaStreamTrack.getConstraints().facingMode) {\n        const facingModeConstraint = mediaStreamTrack.getConstraints().facingMode;\n        return WebcamComponent.getValueFromConstrainDOMString(facingModeConstraint);\n      }\n    }\n  }\n  /**\r\n   * Determines whether the given mediaStreamTrack claims itself as user facing\r\n   * @param mediaStreamTrack\r\n   */\n  static isUserFacing(mediaStreamTrack) {\n    const facingMode = WebcamComponent.getFacingModeFromMediaStreamTrack(mediaStreamTrack);\n    return facingMode ? 'user' === facingMode.toLowerCase() : false;\n  }\n  /**\r\n   * Extracts the value from the given ConstrainDOMString\r\n   * @param constrainDOMString\r\n   */\n  static getValueFromConstrainDOMString(constrainDOMString) {\n    if (constrainDOMString) {\n      if (constrainDOMString instanceof String) {\n        return String(constrainDOMString);\n      } else if (Array.isArray(constrainDOMString) && Array(constrainDOMString).length > 0) {\n        return String(constrainDOMString[0]);\n      } else if (typeof constrainDOMString === 'object') {\n        if (constrainDOMString['exact']) {\n          return String(constrainDOMString['exact']);\n        } else if (constrainDOMString['ideal']) {\n          return String(constrainDOMString['ideal']);\n        }\n      }\n    }\n    return null;\n  }\n  ngAfterViewInit() {\n    this.detectAvailableDevices().then(() => {\n      // start video\n      this.switchToVideoInput(null);\n    }).catch(err => {\n      this.initError.next({\n        message: err\n      });\n      // fallback: still try to load webcam, even if device enumeration failed\n      this.switchToVideoInput(null);\n    });\n  }\n  ngOnDestroy() {\n    this.stopMediaTracks();\n    this.unsubscribeFromSubscriptions();\n  }\n  /**\r\n   * Takes a snapshot of the current webcam's view and emits the image as an event\r\n   */\n  takeSnapshot() {\n    // set canvas size to actual video size\n    const _video = this.nativeVideoElement;\n    const dimensions = {\n      width: this.width,\n      height: this.height\n    };\n    if (_video.videoWidth) {\n      dimensions.width = _video.videoWidth;\n      dimensions.height = _video.videoHeight;\n    }\n    const _canvas = this.canvas.nativeElement;\n    _canvas.width = dimensions.width;\n    _canvas.height = dimensions.height;\n    // paint snapshot image to canvas\n    const context2d = _canvas.getContext('2d');\n    context2d.drawImage(_video, 0, 0);\n    // read canvas content as image\n    const mimeType = this.imageType ? this.imageType : WebcamComponent.DEFAULT_IMAGE_TYPE;\n    const quality = this.imageQuality ? this.imageQuality : WebcamComponent.DEFAULT_IMAGE_QUALITY;\n    const dataUrl = _canvas.toDataURL(mimeType, quality);\n    // get the ImageData object from the canvas' context.\n    let imageData = null;\n    if (this.captureImageData) {\n      imageData = context2d.getImageData(0, 0, _canvas.width, _canvas.height);\n    }\n    this.imageCapture.next(new WebcamImage(dataUrl, mimeType, imageData));\n  }\n  /**\r\n   * Switches to the next/previous video device\r\n   * @param forward\r\n   */\n  rotateVideoInput(forward) {\n    if (this.availableVideoInputs && this.availableVideoInputs.length > 1) {\n      const increment = forward ? 1 : this.availableVideoInputs.length - 1;\n      const nextInputIndex = (this.activeVideoInputIndex + increment) % this.availableVideoInputs.length;\n      this.switchToVideoInput(this.availableVideoInputs[nextInputIndex].deviceId);\n    }\n  }\n  /**\r\n   * Switches the camera-view to the specified video device\r\n   */\n  switchToVideoInput(deviceId) {\n    this.videoInitialized = false;\n    this.stopMediaTracks();\n    this.initWebcam(deviceId, this.videoOptions);\n  }\n  /**\r\n   * Event-handler for video resize event.\r\n   * Triggers Angular change detection so that new video dimensions get applied\r\n   */\n  videoResize() {\n    // here to trigger Angular change detection\n  }\n  get videoWidth() {\n    const videoRatio = this.getVideoAspectRatio();\n    return Math.min(this.width, this.height * videoRatio);\n  }\n  get videoHeight() {\n    const videoRatio = this.getVideoAspectRatio();\n    return Math.min(this.height, this.width / videoRatio);\n  }\n  get videoStyleClasses() {\n    let classes = '';\n    if (this.isMirrorImage()) {\n      classes += 'mirrored ';\n    }\n    return classes.trim();\n  }\n  get nativeVideoElement() {\n    return this.video.nativeElement;\n  }\n  /**\r\n   * Returns the video aspect ratio of the active video stream\r\n   */\n  getVideoAspectRatio() {\n    // calculate ratio from video element dimensions if present\n    const videoElement = this.nativeVideoElement;\n    if (videoElement.videoWidth && videoElement.videoWidth > 0 && videoElement.videoHeight && videoElement.videoHeight > 0) {\n      return videoElement.videoWidth / videoElement.videoHeight;\n    }\n    // nothing present - calculate ratio based on width/height params\n    return this.width / this.height;\n  }\n  /**\r\n   * Init webcam live view\r\n   */\n  initWebcam(deviceId, userVideoTrackConstraints) {\n    const _video = this.nativeVideoElement;\n    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\n      // merge deviceId -> userVideoTrackConstraints\n      const videoTrackConstraints = WebcamComponent.getMediaConstraintsForDevice(deviceId, userVideoTrackConstraints);\n      navigator.mediaDevices.getUserMedia({\n        video: videoTrackConstraints\n      }).then(stream => {\n        this.mediaStream = stream;\n        _video.srcObject = stream;\n        _video.play();\n        this.activeVideoSettings = stream.getVideoTracks()[0].getSettings();\n        const activeDeviceId = WebcamComponent.getDeviceIdFromMediaStreamTrack(stream.getVideoTracks()[0]);\n        this.cameraSwitched.next(activeDeviceId);\n        // Initial detect may run before user gave permissions, returning no deviceIds. This prevents later camera switches. (#47)\n        // Run detect once again within getUserMedia callback, to make sure this time we have permissions and get deviceIds.\n        this.detectAvailableDevices().then(() => {\n          this.activeVideoInputIndex = activeDeviceId ? this.availableVideoInputs.findIndex(mediaDeviceInfo => mediaDeviceInfo.deviceId === activeDeviceId) : -1;\n          this.videoInitialized = true;\n        }).catch(() => {\n          this.activeVideoInputIndex = -1;\n          this.videoInitialized = true;\n        });\n      }).catch(err => {\n        this.initError.next({\n          message: err.message,\n          mediaStreamError: err\n        });\n      });\n    } else {\n      this.initError.next({\n        message: 'Cannot read UserMedia from MediaDevices.'\n      });\n    }\n  }\n  getActiveVideoTrack() {\n    return this.mediaStream ? this.mediaStream.getVideoTracks()[0] : null;\n  }\n  isMirrorImage() {\n    if (!this.getActiveVideoTrack()) {\n      return false;\n    }\n    // check for explicit mirror override parameter\n    {\n      let mirror = 'auto';\n      if (this.mirrorImage) {\n        if (typeof this.mirrorImage === 'string') {\n          mirror = String(this.mirrorImage).toLowerCase();\n        } else {\n          // WebcamMirrorProperties\n          if (this.mirrorImage.x) {\n            mirror = this.mirrorImage.x.toLowerCase();\n          }\n        }\n      }\n      switch (mirror) {\n        case 'always':\n          return true;\n        case 'never':\n          return false;\n      }\n    }\n    // default: enable mirroring if webcam is user facing\n    return WebcamComponent.isUserFacing(this.getActiveVideoTrack());\n  }\n  /**\r\n   * Stops all active media tracks.\r\n   * This prevents the webcam from being indicated as active,\r\n   * even if it is no longer used by this component.\r\n   */\n  stopMediaTracks() {\n    if (this.mediaStream && this.mediaStream.getTracks) {\n      // pause video to prevent mobile browser freezes\n      this.nativeVideoElement.pause();\n      // getTracks() returns all media tracks (video+audio)\n      this.mediaStream.getTracks().forEach(track => track.stop());\n    }\n  }\n  /**\r\n   * Unsubscribe from all open subscriptions\r\n   */\n  unsubscribeFromSubscriptions() {\n    if (this.triggerSubscription) {\n      this.triggerSubscription.unsubscribe();\n    }\n    if (this.switchCameraSubscription) {\n      this.switchCameraSubscription.unsubscribe();\n    }\n  }\n  /**\r\n   * Reads available input devices\r\n   */\n  detectAvailableDevices() {\n    return new Promise((resolve, reject) => {\n      WebcamUtil.getAvailableVideoInputs().then(devices => {\n        this.availableVideoInputs = devices;\n        resolve(devices);\n      }).catch(err => {\n        this.availableVideoInputs = [];\n        reject(err);\n      });\n    });\n  }\n}\nWebcamComponent.DEFAULT_VIDEO_OPTIONS = {\n  facingMode: 'environment'\n};\nWebcamComponent.DEFAULT_IMAGE_TYPE = 'image/jpeg';\nWebcamComponent.DEFAULT_IMAGE_QUALITY = 0.92;\nWebcamComponent.ɵfac = function WebcamComponent_Factory(t) {\n  return new (t || WebcamComponent)();\n};\nWebcamComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: WebcamComponent,\n  selectors: [[\"webcam\"]],\n  viewQuery: function WebcamComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7);\n      i0.ɵɵviewQuery(_c1, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.video = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.canvas = _t.first);\n    }\n  },\n  inputs: {\n    width: \"width\",\n    height: \"height\",\n    videoOptions: \"videoOptions\",\n    allowCameraSwitch: \"allowCameraSwitch\",\n    mirrorImage: \"mirrorImage\",\n    captureImageData: \"captureImageData\",\n    imageType: \"imageType\",\n    imageQuality: \"imageQuality\",\n    trigger: \"trigger\",\n    switchCamera: \"switchCamera\"\n  },\n  outputs: {\n    imageCapture: \"imageCapture\",\n    initError: \"initError\",\n    imageClick: \"imageClick\",\n    cameraSwitched: \"cameraSwitched\"\n  },\n  decls: 6,\n  vars: 7,\n  consts: [[1, \"webcam-wrapper\", 3, \"click\"], [\"autoplay\", \"\", \"muted\", \"\", \"playsinline\", \"\", 3, \"width\", \"height\", \"resize\"], [\"video\", \"\"], [\"class\", \"camera-switch\", 3, \"click\", 4, \"ngIf\"], [3, \"width\", \"height\"], [\"canvas\", \"\"], [1, \"camera-switch\", 3, \"click\"]],\n  template: function WebcamComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵlistener(\"click\", function WebcamComponent_Template_div_click_0_listener() {\n        return ctx.imageClick.next();\n      });\n      i0.ɵɵelementStart(1, \"video\", 1, 2);\n      i0.ɵɵlistener(\"resize\", function WebcamComponent_Template_video_resize_1_listener() {\n        return ctx.videoResize();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(3, WebcamComponent_div_3_Template, 1, 0, \"div\", 3);\n      i0.ɵɵelement(4, \"canvas\", 4, 5);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵclassMap(ctx.videoStyleClasses);\n      i0.ɵɵproperty(\"width\", ctx.videoWidth)(\"height\", ctx.videoHeight);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.allowCameraSwitch && ctx.availableVideoInputs.length > 1 && ctx.videoInitialized);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"width\", ctx.width)(\"height\", ctx.height);\n    }\n  },\n  dependencies: [i1.NgIf],\n  styles: [\".webcam-wrapper[_ngcontent-%COMP%]{display:inline-block;position:relative;line-height:0}.webcam-wrapper[_ngcontent-%COMP%]   video.mirrored[_ngcontent-%COMP%]{transform:scaleX(-1)}.webcam-wrapper[_ngcontent-%COMP%]   canvas[_ngcontent-%COMP%]{display:none}.webcam-wrapper[_ngcontent-%COMP%]   .camera-switch[_ngcontent-%COMP%]{background-color:#0000001a;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAE9UlEQVR42u2aT2hdRRTGf+cRQqghSqihdBFDkRISK2KDfzDWxHaRQHEhaINKqa1gKQhd6EZLN+IidCH+Q0oWIkVRC21BQxXRitVaSbKoJSGtYGoK2tQ/tU1jY5v0c5F54Xl7b/KSO/PyEt+3e5f75p7zzZwzZ74zUEIJJfyfYaEGllQGVAGZlENdBy6Z2cSiYFTSKkkfS/pH/nBF0kFJdUW9AiRVASeAukD8DgNrzOySrwEzng18KaDzALXuG8W3AiStAvqBisBRNg40mtlPxbYCOgvgPO4bncWW+JpVeDQXRQhIygDfA00F5r0XuNfMrgclQFI98DDQCNQA5ZFXqoCWBVp8XwHRHeEqcN7loy/NbHBesyqpQ1KfFj/6nC+ZvFaApFrgPaCZpYVvgCfNbDiRAElNwGFg+RIt/X8H2s2s9wYCJDUAR4HqJX7++RN40MwGpgmQVAH0AQ2BPz4AHHPl8nBOAqtyFWQjsA6oL4Ada81sPDv7uwImod8kvSJp9RyS8O2SXnb/DYVd2Y9VSroQ4ANXJO2WVJmixqh0kzMWwL4LkiqRtDnA4D1zmfE8j9g9AezcnAHaPcfXdbfdnPZ2Yps6+DwAvO/Z1naTdApY7Xng48BDZnY1MpMVQBuw3iXc5Tnb0wBwBPjUzP6eoezuArZ6svM0geJLkvZEYnl3nkntoqROSbckSW2Suj3ZOIangc7GPJuUtNGdFIfmMeavktoSSKiW9LMPw30Q8JqkekmjCbOZRhuclLQjgYSNxUBAj6RyZ9ATgUJpUtJTCSR8vpAEXHAyWK5BXYFIGHOlepSAloUk4NEYgyoknQhEwhFJ0e8h6VSaQeerCb5uZgdi9utxYBNwOUD93hIVXswM4INCi6K9wAszFC2DwLOBDjHbYp59karIUnRdzYy/3ClqVklaUhfwTICj7K25OqA7a4wWagVsm4Me/xzwg2cCqqONFzO7DPxSCAJi436GUBgHHguQD2oTlJ55oSzP9ybccsttSJw1szdjFOSnI/8dTCGZHwcORp4Nx7y3B1iZ8/sm4MW8/Euxg5wIsS/HaAp3zeP4/G7obRDXI4jiTIA22H7Xdc7X+S3A5lC7QBQ357aq3VAjCeSkwUfAJrfvz+R8A9ADLAtZB+TinpjC5JMA+//jwPZZnF8G7J+L8z4IWB/zbG+gIujVWfLBW/NStVMmqaG4POJRsIjix7h8IGnLQuoBbQki5sVAJHyYm7YkNaRRtXwQ8G1cHpX0iKRrgUjYno17Sf0LrQhJUkdCeHWkVITGJI0k1QeS3ikGSUzOyJUJJNznYneuOCnpTldcxa2kP3xJYqOeSDjqZG8ShJLnE8TTuMS6Iyu1BW7djZqkfo9N0QOuYJmYQddfB7RG+gLTNzqAY9FrL+5/nwEbvDdJJe3zzOrhNP3AWRqmk55t3ZcBuj3b2gb0Sbrbo/NNzk7fFzu7s/E5EiC+rrmeQU0Kx2skvRFoOx2ZzlmSdgbsw49JetvtBpk8nM64d/cGbNtJ0s7cGyJlwHeEv+t3nqnLSgPAUOSGyG3AHUxdzqoJbEcvcL+ZTeTeEapzJKxgaeOcc/7Mf06D7kFrguS0VDAMtGadv+E47DT9tcChJej8ISfpD+abgTe45uOkFi8mnQ+JBVQ+d4VXuOptjavcyot8pq86mfwk8LWZnaOEEkoooYQSSojDv8AhQNeGfe0jAAAAAElFTkSuQmCC);background-repeat:no-repeat;border-radius:5px;position:absolute;right:13px;top:10px;height:48px;width:48px;background-size:80%;cursor:pointer;background-position:center;transition:background-color .2s ease}.webcam-wrapper[_ngcontent-%COMP%]   .camera-switch[_ngcontent-%COMP%]:hover{background-color:#0000002e}\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(WebcamComponent, [{\n    type: Component,\n    args: [{\n      selector: 'webcam',\n      template: \"<div class=\\\"webcam-wrapper\\\" (click)=\\\"imageClick.next();\\\">\\r\\n  <video #video [width]=\\\"videoWidth\\\" [height]=\\\"videoHeight\\\" [class]=\\\"videoStyleClasses\\\" autoplay muted playsinline (resize)=\\\"videoResize()\\\"></video>\\r\\n  <div class=\\\"camera-switch\\\" *ngIf=\\\"allowCameraSwitch && availableVideoInputs.length > 1 && videoInitialized\\\" (click)=\\\"rotateVideoInput(true)\\\"></div>\\r\\n  <canvas #canvas [width]=\\\"width\\\" [height]=\\\"height\\\"></canvas>\\r\\n</div>\\r\\n\",\n      styles: [\".webcam-wrapper{display:inline-block;position:relative;line-height:0}.webcam-wrapper video.mirrored{transform:scaleX(-1)}.webcam-wrapper canvas{display:none}.webcam-wrapper .camera-switch{background-color:#0000001a;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAE9UlEQVR42u2aT2hdRRTGf+cRQqghSqihdBFDkRISK2KDfzDWxHaRQHEhaINKqa1gKQhd6EZLN+IidCH+Q0oWIkVRC21BQxXRitVaSbKoJSGtYGoK2tQ/tU1jY5v0c5F54Xl7b/KSO/PyEt+3e5f75p7zzZwzZ74zUEIJJfyfYaEGllQGVAGZlENdBy6Z2cSiYFTSKkkfS/pH/nBF0kFJdUW9AiRVASeAukD8DgNrzOySrwEzng18KaDzALXuG8W3AiStAvqBisBRNg40mtlPxbYCOgvgPO4bncWW+JpVeDQXRQhIygDfA00F5r0XuNfMrgclQFI98DDQCNQA5ZFXqoCWBVp8XwHRHeEqcN7loy/NbHBesyqpQ1KfFj/6nC+ZvFaApFrgPaCZpYVvgCfNbDiRAElNwGFg+RIt/X8H2s2s9wYCJDUAR4HqJX7++RN40MwGpgmQVAH0AQ2BPz4AHHPl8nBOAqtyFWQjsA6oL4Ada81sPDv7uwImod8kvSJp9RyS8O2SXnb/DYVd2Y9VSroQ4ANXJO2WVJmixqh0kzMWwL4LkiqRtDnA4D1zmfE8j9g9AezcnAHaPcfXdbfdnPZ2Yps6+DwAvO/Z1naTdApY7Xng48BDZnY1MpMVQBuw3iXc5Tnb0wBwBPjUzP6eoezuArZ6svM0geJLkvZEYnl3nkntoqROSbckSW2Suj3ZOIangc7GPJuUtNGdFIfmMeavktoSSKiW9LMPw30Q8JqkekmjCbOZRhuclLQjgYSNxUBAj6RyZ9ATgUJpUtJTCSR8vpAEXHAyWK5BXYFIGHOlepSAloUk4NEYgyoknQhEwhFJ0e8h6VSaQeerCb5uZgdi9utxYBNwOUD93hIVXswM4INCi6K9wAszFC2DwLOBDjHbYp59karIUnRdzYy/3ClqVklaUhfwTICj7K25OqA7a4wWagVsm4Me/xzwg2cCqqONFzO7DPxSCAJi436GUBgHHguQD2oTlJ55oSzP9ybccsttSJw1szdjFOSnI/8dTCGZHwcORp4Nx7y3B1iZ8/sm4MW8/Euxg5wIsS/HaAp3zeP4/G7obRDXI4jiTIA22H7Xdc7X+S3A5lC7QBQ357aq3VAjCeSkwUfAJrfvz+R8A9ADLAtZB+TinpjC5JMA+//jwPZZnF8G7J+L8z4IWB/zbG+gIujVWfLBW/NStVMmqaG4POJRsIjix7h8IGnLQuoBbQki5sVAJHyYm7YkNaRRtXwQ8G1cHpX0iKRrgUjYno17Sf0LrQhJUkdCeHWkVITGJI0k1QeS3ikGSUzOyJUJJNznYneuOCnpTldcxa2kP3xJYqOeSDjqZG8ShJLnE8TTuMS6Iyu1BW7djZqkfo9N0QOuYJmYQddfB7RG+gLTNzqAY9FrL+5/nwEbvDdJJe3zzOrhNP3AWRqmk55t3ZcBuj3b2gb0Sbrbo/NNzk7fFzu7s/E5EiC+rrmeQU0Kx2skvRFoOx2ZzlmSdgbsw49JetvtBpk8nM64d/cGbNtJ0s7cGyJlwHeEv+t3nqnLSgPAUOSGyG3AHUxdzqoJbEcvcL+ZTeTeEapzJKxgaeOcc/7Mf06D7kFrguS0VDAMtGadv+E47DT9tcChJej8ISfpD+abgTe45uOkFi8mnQ+JBVQ+d4VXuOptjavcyot8pq86mfwk8LWZnaOEEkoooYQSSojDv8AhQNeGfe0jAAAAAElFTkSuQmCC);background-repeat:no-repeat;border-radius:5px;position:absolute;right:13px;top:10px;height:48px;width:48px;background-size:80%;cursor:pointer;background-position:center;transition:background-color .2s ease}.webcam-wrapper .camera-switch:hover{background-color:#0000002e}\\n\"]\n    }]\n  }], null, {\n    width: [{\n      type: Input\n    }],\n    height: [{\n      type: Input\n    }],\n    videoOptions: [{\n      type: Input\n    }],\n    allowCameraSwitch: [{\n      type: Input\n    }],\n    mirrorImage: [{\n      type: Input\n    }],\n    captureImageData: [{\n      type: Input\n    }],\n    imageType: [{\n      type: Input\n    }],\n    imageQuality: [{\n      type: Input\n    }],\n    imageCapture: [{\n      type: Output\n    }],\n    initError: [{\n      type: Output\n    }],\n    imageClick: [{\n      type: Output\n    }],\n    cameraSwitched: [{\n      type: Output\n    }],\n    video: [{\n      type: ViewChild,\n      args: ['video', {\n        static: true\n      }]\n    }],\n    canvas: [{\n      type: ViewChild,\n      args: ['canvas', {\n        static: true\n      }]\n    }],\n    trigger: [{\n      type: Input\n    }],\n    switchCamera: [{\n      type: Input\n    }]\n  });\n})();\nconst COMPONENTS = [WebcamComponent];\nclass WebcamModule {}\nWebcamModule.ɵfac = function WebcamModule_Factory(t) {\n  return new (t || WebcamModule)();\n};\nWebcamModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: WebcamModule,\n  declarations: [WebcamComponent],\n  imports: [CommonModule],\n  exports: [WebcamComponent]\n});\nWebcamModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule]]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(WebcamModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [COMPONENTS],\n      exports: [COMPONENTS]\n    }]\n  }], null, null);\n})();\nclass WebcamInitError {\n  constructor() {\n    this.message = null;\n    this.mediaStreamError = null;\n  }\n}\nclass WebcamMirrorProperties {}\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { WebcamComponent, WebcamImage, WebcamInitError, WebcamMirrorProperties, WebcamModule, WebcamUtil };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,CAAC,QAAQ;AACrB,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,SAAS,SAAS,sDAAsD;AACpF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,IAAI,CAAC;AAAA,IACrD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACF;AACA,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,YAAY,gBAAgB,UAAU,WAAW;AAC/C,SAAK,YAAY;AACjB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB;AACvB,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,kBAAkB;AACvB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,mBAAmB,SAAS,UAAU;AAC3C,WAAO,QAAQ,QAAQ,QAAQ,QAAQ,YAAY,EAAE;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,gBAAgB;AAClB,WAAO,KAAK,iBAAiB,KAAK,iBAAiB,KAAK,iBAAiB,aAAY,mBAAmB,KAAK,iBAAiB,KAAK,SAAS;AAAA,EAC9I;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,iBAAiB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AACF;AACA,IAAM,aAAN,MAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,OAAO,0BAA0B;AAC/B,QAAI,CAAC,UAAU,gBAAgB,CAAC,UAAU,aAAa,kBAAkB;AACvE,aAAO,QAAQ,OAAO,mCAAmC;AAAA,IAC3D;AACA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,gBAAU,aAAa,iBAAiB,EAAE,KAAK,aAAW;AACxD,gBAAQ,QAAQ,OAAO,YAAU,OAAO,SAAS,YAAY,CAAC;AAAA,MAChE,CAAC,EAAE,MAAM,SAAO;AACd,eAAO,IAAI,WAAW,GAAG;AAAA,MAC3B,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc;AAEZ,SAAK,QAAQ;AAEb,SAAK,SAAS;AAEd,SAAK,eAAe,iBAAgB;AAEpC,SAAK,oBAAoB;AAEzB,SAAK,mBAAmB;AAExB,SAAK,YAAY,iBAAgB;AAEjC,SAAK,eAAe,iBAAgB;AAEpC,SAAK,eAAe,IAAI,aAAa;AAErC,SAAK,YAAY,IAAI,aAAa;AAElC,SAAK,aAAa,IAAI,aAAa;AAEnC,SAAK,iBAAiB,IAAI,aAAa;AAEvC,SAAK,uBAAuB,CAAC;AAE7B,SAAK,mBAAmB;AAExB,SAAK,wBAAwB;AAE7B,SAAK,cAAc;AAEnB,SAAK,sBAAsB;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,QAAQ,SAAS;AACnB,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,YAAY;AAAA,IACvC;AAEA,SAAK,sBAAsB,QAAQ,UAAU,MAAM;AACjD,WAAK,aAAa;AAAA,IACpB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,aAAa,cAAc;AAC7B,QAAI,KAAK,0BAA0B;AACjC,WAAK,yBAAyB,YAAY;AAAA,IAC5C;AAEA,SAAK,2BAA2B,aAAa,UAAU,WAAS;AAC9D,UAAI,OAAO,UAAU,UAAU;AAE7B,aAAK,mBAAmB,KAAK;AAAA,MAC/B,OAAO;AAEL,aAAK,iBAAiB,UAAU,KAAK;AAAA,MACvC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,6BAA6B,UAAU,2BAA2B;AACvE,UAAM,SAAS,4BAA4B,4BAA4B,KAAK;AAC5E,QAAI,UAAU;AACZ,aAAO,WAAW;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,gCAAgC,kBAAkB;AACvD,QAAI,iBAAiB,eAAe,iBAAiB,YAAY,KAAK,iBAAiB,YAAY,EAAE,UAAU;AAC7G,aAAO,iBAAiB,YAAY,EAAE;AAAA,IACxC,WAAW,iBAAiB,kBAAkB,iBAAiB,eAAe,KAAK,iBAAiB,eAAe,EAAE,UAAU;AAC7H,YAAM,cAAc,iBAAiB,eAAe,EAAE;AACtD,aAAO,iBAAgB,+BAA+B,WAAW;AAAA,IACnE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,kCAAkC,kBAAkB;AACzD,QAAI,kBAAkB;AACpB,UAAI,iBAAiB,eAAe,iBAAiB,YAAY,KAAK,iBAAiB,YAAY,EAAE,YAAY;AAC/G,eAAO,iBAAiB,YAAY,EAAE;AAAA,MACxC,WAAW,iBAAiB,kBAAkB,iBAAiB,eAAe,KAAK,iBAAiB,eAAe,EAAE,YAAY;AAC/H,cAAM,uBAAuB,iBAAiB,eAAe,EAAE;AAC/D,eAAO,iBAAgB,+BAA+B,oBAAoB;AAAA,MAC5E;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,aAAa,kBAAkB;AACpC,UAAM,aAAa,iBAAgB,kCAAkC,gBAAgB;AACrF,WAAO,aAAa,WAAW,WAAW,YAAY,IAAI;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,+BAA+B,oBAAoB;AACxD,QAAI,oBAAoB;AACtB,UAAI,8BAA8B,QAAQ;AACxC,eAAO,OAAO,kBAAkB;AAAA,MAClC,WAAW,MAAM,QAAQ,kBAAkB,KAAK,MAAM,kBAAkB,EAAE,SAAS,GAAG;AACpF,eAAO,OAAO,mBAAmB,CAAC,CAAC;AAAA,MACrC,WAAW,OAAO,uBAAuB,UAAU;AACjD,YAAI,mBAAmB,OAAO,GAAG;AAC/B,iBAAO,OAAO,mBAAmB,OAAO,CAAC;AAAA,QAC3C,WAAW,mBAAmB,OAAO,GAAG;AACtC,iBAAO,OAAO,mBAAmB,OAAO,CAAC;AAAA,QAC3C;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAChB,SAAK,uBAAuB,EAAE,KAAK,MAAM;AAEvC,WAAK,mBAAmB,IAAI;AAAA,IAC9B,CAAC,EAAE,MAAM,SAAO;AACd,WAAK,UAAU,KAAK;AAAA,QAClB,SAAS;AAAA,MACX,CAAC;AAED,WAAK,mBAAmB,IAAI;AAAA,IAC9B,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,gBAAgB;AACrB,SAAK,6BAA6B;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AAEb,UAAM,SAAS,KAAK;AACpB,UAAM,aAAa;AAAA,MACjB,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,IACf;AACA,QAAI,OAAO,YAAY;AACrB,iBAAW,QAAQ,OAAO;AAC1B,iBAAW,SAAS,OAAO;AAAA,IAC7B;AACA,UAAM,UAAU,KAAK,OAAO;AAC5B,YAAQ,QAAQ,WAAW;AAC3B,YAAQ,SAAS,WAAW;AAE5B,UAAM,YAAY,QAAQ,WAAW,IAAI;AACzC,cAAU,UAAU,QAAQ,GAAG,CAAC;AAEhC,UAAM,WAAW,KAAK,YAAY,KAAK,YAAY,iBAAgB;AACnE,UAAM,UAAU,KAAK,eAAe,KAAK,eAAe,iBAAgB;AACxE,UAAM,UAAU,QAAQ,UAAU,UAAU,OAAO;AAEnD,QAAI,YAAY;AAChB,QAAI,KAAK,kBAAkB;AACzB,kBAAY,UAAU,aAAa,GAAG,GAAG,QAAQ,OAAO,QAAQ,MAAM;AAAA,IACxE;AACA,SAAK,aAAa,KAAK,IAAI,YAAY,SAAS,UAAU,SAAS,CAAC;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,SAAS;AACxB,QAAI,KAAK,wBAAwB,KAAK,qBAAqB,SAAS,GAAG;AACrE,YAAM,YAAY,UAAU,IAAI,KAAK,qBAAqB,SAAS;AACnE,YAAM,kBAAkB,KAAK,wBAAwB,aAAa,KAAK,qBAAqB;AAC5F,WAAK,mBAAmB,KAAK,qBAAqB,cAAc,EAAE,QAAQ;AAAA,IAC5E;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB,UAAU;AAC3B,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,WAAW,UAAU,KAAK,YAAY;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AAAA,EAEd;AAAA,EACA,IAAI,aAAa;AACf,UAAM,aAAa,KAAK,oBAAoB;AAC5C,WAAO,KAAK,IAAI,KAAK,OAAO,KAAK,SAAS,UAAU;AAAA,EACtD;AAAA,EACA,IAAI,cAAc;AAChB,UAAM,aAAa,KAAK,oBAAoB;AAC5C,WAAO,KAAK,IAAI,KAAK,QAAQ,KAAK,QAAQ,UAAU;AAAA,EACtD;AAAA,EACA,IAAI,oBAAoB;AACtB,QAAI,UAAU;AACd,QAAI,KAAK,cAAc,GAAG;AACxB,iBAAW;AAAA,IACb;AACA,WAAO,QAAQ,KAAK;AAAA,EACtB;AAAA,EACA,IAAI,qBAAqB;AACvB,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AAEpB,UAAM,eAAe,KAAK;AAC1B,QAAI,aAAa,cAAc,aAAa,aAAa,KAAK,aAAa,eAAe,aAAa,cAAc,GAAG;AACtH,aAAO,aAAa,aAAa,aAAa;AAAA,IAChD;AAEA,WAAO,KAAK,QAAQ,KAAK;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,UAAU,2BAA2B;AAC9C,UAAM,SAAS,KAAK;AACpB,QAAI,UAAU,gBAAgB,UAAU,aAAa,cAAc;AAEjE,YAAM,wBAAwB,iBAAgB,6BAA6B,UAAU,yBAAyB;AAC9G,gBAAU,aAAa,aAAa;AAAA,QAClC,OAAO;AAAA,MACT,CAAC,EAAE,KAAK,YAAU;AAChB,aAAK,cAAc;AACnB,eAAO,YAAY;AACnB,eAAO,KAAK;AACZ,aAAK,sBAAsB,OAAO,eAAe,EAAE,CAAC,EAAE,YAAY;AAClE,cAAM,iBAAiB,iBAAgB,gCAAgC,OAAO,eAAe,EAAE,CAAC,CAAC;AACjG,aAAK,eAAe,KAAK,cAAc;AAGvC,aAAK,uBAAuB,EAAE,KAAK,MAAM;AACvC,eAAK,wBAAwB,iBAAiB,KAAK,qBAAqB,UAAU,qBAAmB,gBAAgB,aAAa,cAAc,IAAI;AACpJ,eAAK,mBAAmB;AAAA,QAC1B,CAAC,EAAE,MAAM,MAAM;AACb,eAAK,wBAAwB;AAC7B,eAAK,mBAAmB;AAAA,QAC1B,CAAC;AAAA,MACH,CAAC,EAAE,MAAM,SAAO;AACd,aAAK,UAAU,KAAK;AAAA,UAClB,SAAS,IAAI;AAAA,UACb,kBAAkB;AAAA,QACpB,CAAC;AAAA,MACH,CAAC;AAAA,IACH,OAAO;AACL,WAAK,UAAU,KAAK;AAAA,QAClB,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,cAAc,KAAK,YAAY,eAAe,EAAE,CAAC,IAAI;AAAA,EACnE;AAAA,EACA,gBAAgB;AACd,QAAI,CAAC,KAAK,oBAAoB,GAAG;AAC/B,aAAO;AAAA,IACT;AAEA;AACE,UAAI,SAAS;AACb,UAAI,KAAK,aAAa;AACpB,YAAI,OAAO,KAAK,gBAAgB,UAAU;AACxC,mBAAS,OAAO,KAAK,WAAW,EAAE,YAAY;AAAA,QAChD,OAAO;AAEL,cAAI,KAAK,YAAY,GAAG;AACtB,qBAAS,KAAK,YAAY,EAAE,YAAY;AAAA,UAC1C;AAAA,QACF;AAAA,MACF;AACA,cAAQ,QAAQ;AAAA,QACd,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,MACX;AAAA,IACF;AAEA,WAAO,iBAAgB,aAAa,KAAK,oBAAoB,CAAC;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,QAAI,KAAK,eAAe,KAAK,YAAY,WAAW;AAElD,WAAK,mBAAmB,MAAM;AAE9B,WAAK,YAAY,UAAU,EAAE,QAAQ,WAAS,MAAM,KAAK,CAAC;AAAA,IAC5D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,+BAA+B;AAC7B,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,YAAY;AAAA,IACvC;AACA,QAAI,KAAK,0BAA0B;AACjC,WAAK,yBAAyB,YAAY;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,yBAAyB;AACvB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,iBAAW,wBAAwB,EAAE,KAAK,aAAW;AACnD,aAAK,uBAAuB;AAC5B,gBAAQ,OAAO;AAAA,MACjB,CAAC,EAAE,MAAM,SAAO;AACd,aAAK,uBAAuB,CAAC;AAC7B,eAAO,GAAG;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,gBAAgB,wBAAwB;AAAA,EACtC,YAAY;AACd;AACA,gBAAgB,qBAAqB;AACrC,gBAAgB,wBAAwB;AACxC,gBAAgB,OAAO,SAAS,wBAAwB,GAAG;AACzD,SAAO,KAAK,KAAK,iBAAiB;AACpC;AACA,gBAAgB,OAAyB,kBAAkB;AAAA,EACzD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,QAAQ,CAAC;AAAA,EACtB,WAAW,SAAS,sBAAsB,IAAI,KAAK;AACjD,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,KAAK,CAAC;AACrB,MAAG,YAAY,KAAK,CAAC;AAAA,IACvB;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ,GAAG;AAC5D,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS,GAAG;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,SAAS;AAAA,IACT,cAAc;AAAA,EAChB;AAAA,EACA,SAAS;AAAA,IACP,cAAc;AAAA,IACd,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,gBAAgB;AAAA,EAClB;AAAA,EACA,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,kBAAkB,GAAG,OAAO,GAAG,CAAC,YAAY,IAAI,SAAS,IAAI,eAAe,IAAI,GAAG,SAAS,UAAU,QAAQ,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,SAAS,iBAAiB,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,QAAQ,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,iBAAiB,GAAG,OAAO,CAAC;AAAA,EACxQ,UAAU,SAAS,yBAAyB,IAAI,KAAK;AACnD,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,MAAG,WAAW,SAAS,SAAS,gDAAgD;AAC9E,eAAO,IAAI,WAAW,KAAK;AAAA,MAC7B,CAAC;AACD,MAAG,eAAe,GAAG,SAAS,GAAG,CAAC;AAClC,MAAG,WAAW,UAAU,SAAS,mDAAmD;AAClF,eAAO,IAAI,YAAY;AAAA,MACzB,CAAC;AACD,MAAG,aAAa;AAChB,MAAG,WAAW,GAAG,gCAAgC,GAAG,GAAG,OAAO,CAAC;AAC/D,MAAG,UAAU,GAAG,UAAU,GAAG,CAAC;AAC9B,MAAG,aAAa;AAAA,IAClB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,IAAI,iBAAiB;AACnC,MAAG,WAAW,SAAS,IAAI,UAAU,EAAE,UAAU,IAAI,WAAW;AAChE,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,QAAQ,IAAI,qBAAqB,IAAI,qBAAqB,SAAS,KAAK,IAAI,gBAAgB;AAC1G,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,SAAS,IAAI,KAAK,EAAE,UAAU,IAAI,MAAM;AAAA,IACxD;AAAA,EACF;AAAA,EACA,cAAc,CAAI,IAAI;AAAA,EACtB,QAAQ,CAAC,+6EAA+6E;AAC17E,CAAC;AAAA,CACA,WAAY;AACX,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ,CAAC,8vEAA8vE;AAAA,IACzwE,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,QACd,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,aAAa,CAAC,eAAe;AACnC,IAAM,eAAN,MAAmB;AAAC;AACpB,aAAa,OAAO,SAAS,qBAAqB,GAAG;AACnD,SAAO,KAAK,KAAK,cAAc;AACjC;AACA,aAAa,OAAyB,iBAAiB;AAAA,EACrD,MAAM;AAAA,EACN,cAAc,CAAC,eAAe;AAAA,EAC9B,SAAS,CAAC,YAAY;AAAA,EACtB,SAAS,CAAC,eAAe;AAC3B,CAAC;AACD,aAAa,OAAyB,iBAAiB;AAAA,EACrD,SAAS,CAAC,CAAC,YAAY,CAAC;AAC1B,CAAC;AAAA,CACA,WAAY;AACX,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,MACtB,cAAc,CAAC,UAAU;AAAA,MACzB,SAAS,CAAC,UAAU;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAN,MAAsB;AAAA,EACpB,cAAc;AACZ,SAAK,UAAU;AACf,SAAK,mBAAmB;AAAA,EAC1B;AACF;AACA,IAAM,yBAAN,MAA6B;AAAC;", "names": []}